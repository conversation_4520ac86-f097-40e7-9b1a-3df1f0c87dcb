import express from 'express';
import cors from 'cors';
import { DatabaseUtils } from '../utils/database';
import { dataEndpoints } from './data-endpoints';
import { z } from 'zod';

const app = express();
const port = process.env.PORT || 3000;

// Initialize database utilities
const dbUtils = DatabaseUtils.getInstance();

// Middleware
app.use(cors());
app.use(express.json());

// Mount data endpoints
app.use('/api', dataEndpoints);

// Request validation schemas
const SearchGuidelinesSchema = z.object({
  query: z.string().min(1, 'Query is required'),
  carrier: z.string().optional(),
  category: z.string().optional(),
  limit: z.number().min(1).max(50).default(5)
});

const SearchProceduresSchema = z.object({
  query: z.string().min(1, 'Query is required'),
  limit: z.number().min(1).max(50).default(10)
});

const ValidateCoverageSchema = z.object({
  carrier: z.string().min(1, 'Carrier is required'),
  procedure_codes: z.array(z.string()).min(1, 'At least one procedure code is required'),
  patient_info: z.object({
    age: z.number().optional(),
    member_id: z.string().optional()
  }).optional()
});

const GlossaryLookupSchema = z.object({
  term: z.string().min(1, 'Term is required'),
  exact_match: z.boolean().default(false)
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    service: 'dental-narrator-api'
  });
});

// POST /search/guidelines - Semantic search for dental insurance guidelines
app.post('/search/guidelines', async (req, res) => {
  try {
    const validatedData = SearchGuidelinesSchema.parse(req.body);
    const { query, carrier, category, limit } = validatedData;

    console.log(`🔍 Guidelines search: "${query}" | Carrier: ${carrier || 'Any'} | Category: ${category || 'Any'}`);

    const guidelines = await dbUtils.searchGuidelines(query, {
      carrier: carrier || undefined,
      category: category || undefined,
      limit: limit
    });

    res.json({
      success: true,
      query,
      filters: {
        carrier: carrier || null,
        category: category || null
      },
      results_count: guidelines.length,
      results: guidelines,
      metadata: {
        search_type: 'semantic',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Guidelines search error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /search/procedures - Search for dental procedure information
app.post('/search/procedures', async (req, res) => {
  try {
    const validatedData = SearchProceduresSchema.parse(req.body);
    const { query, limit } = validatedData;

    console.log(`🦷 Procedure search: "${query}"`);

    const procedures = await dbUtils.lookupProcedure(query);

    // Limit results
    const limitedResults = procedures.slice(0, limit);

    res.json({
      success: true,
      query,
      results_count: limitedResults.length,
      total_found: procedures.length,
      results: limitedResults,
      metadata: {
        search_type: 'procedure_lookup',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Procedure search error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /validate/coverage - Validate insurance coverage for procedures
app.post('/validate/coverage', async (req, res) => {
  try {
    const validatedData = ValidateCoverageSchema.parse(req.body);
    const { carrier, procedure_codes, patient_info } = validatedData;

    console.log(`🏥 Coverage validation: ${carrier} | Procedures: ${procedure_codes.join(', ')}`);

    // Look up carrier information
    const carrierInfo = await dbUtils.lookupCarrier(carrier);
    
    if (!carrierInfo) {
      return res.status(404).json({
        success: false,
        error: 'Carrier not found',
        message: `No carrier found matching "${carrier}"`
      });
    }

    // Look up procedure information
    const procedureResults = await Promise.all(
      procedure_codes.map(async (code) => {
        const procedures = await dbUtils.lookupProcedure(code);
        return {
          code,
          found: procedures.length > 0,
          procedure_info: procedures[0] || null
        };
      })
    );

    // Search for coverage guidelines
    const coverageQuery = `${carrier} coverage requirements for ${procedure_codes.join(' ')}`;
    const guidelines = await dbUtils.searchGuidelines(coverageQuery, {
      carrier: carrier,
      limit: 10
    });

    res.json({
      success: true,
      carrier: {
        name: carrierInfo.name,
        payer_id: carrierInfo.payer_id
      },
      procedures: procedureResults,
      coverage_guidelines: guidelines,
      validation_summary: {
        carrier_found: true,
        procedures_found: procedureResults.filter(p => p.found).length,
        total_procedures: procedure_codes.length,
        guidelines_found: guidelines.length
      },
      metadata: {
        timestamp: new Date().toISOString(),
        patient_info: patient_info || null
      }
    });

  } catch (error) {
    console.error('❌ Coverage validation error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /glossary/lookup - Look up dental insurance terminology
app.post('/glossary/lookup', async (req, res) => {
  try {
    const validatedData = GlossaryLookupSchema.parse(req.body);
    const { term, exact_match } = validatedData;

    console.log(`📚 Glossary lookup: "${term}" | Exact: ${exact_match}`);

    // This would use your glossary search functionality
    // For now, providing a basic implementation
    const searchQuery = exact_match ? `"${term}"` : term;
    const guidelines = await dbUtils.searchGuidelines(`${term} definition meaning`, {
      limit: 5
    });

    res.json({
      success: true,
      term,
      exact_match,
      results_count: guidelines.length,
      results: guidelines.map(g => ({
        id: g.id,
        title: g.title,
        content: g.content,
        carrier: g.carrier,
        relevance_score: g.similarity_score
      })),
      metadata: {
        search_type: 'glossary_lookup',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Glossary lookup error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Error handling middleware
app.use((err: any, req: Request, res: Response, next: NextFunction) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: 'An unexpected error occurred'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Not found',
    message: `Endpoint ${req.method} ${req.path} not found`
  });
});

export { app };

// Start server if this file is run directly
if (require.main === module) {
  app.listen(port, () => {
    console.log(`🚀 Dental Narrator API server running on port ${port}`);
    console.log(`📋 Available endpoints:`);
    console.log(`  POST /search/guidelines - Search dental insurance guidelines`);
    console.log(`  POST /search/procedures - Search dental procedures`);
    console.log(`  POST /validate/coverage - Validate insurance coverage`);
    console.log(`  POST /glossary/lookup - Look up dental terminology`);
    console.log(`  GET  /health - Health check`);
  });
}

import express from 'express';
import cors from 'cors';
import { DatabaseUtils } from '../utils/database';
import { dataEndpoints } from './data-endpoints';
import { z } from 'zod';

const app = express();
const port = process.env.PORT || 3000;

// Initialize database utilities
const dbUtils = DatabaseUtils.getInstance();

// Middleware
app.use(cors());
app.use(express.json());

// Mount data endpoints
app.use('/api', dataEndpoints);

// Request validation schemas
const SearchGuidelinesSchema = z.object({
  query: z.string().min(1, 'Query is required'),
  carrier: z.string().optional(),
  category: z.string().optional(),
  limit: z.number().min(1).max(50).default(5)
});

const SearchProceduresSchema = z.object({
  query: z.string().min(1, 'Query is required'),
  limit: z.number().min(1).max(50).default(10)
});

const ValidateCoverageSchema = z.object({
  carrier: z.string().min(1, 'Carrier is required'),
  procedure_codes: z.array(z.string()).min(1, 'At least one procedure code is required'),
  patient_info: z.object({
    age: z.number().optional(),
    member_id: z.string().optional()
  }).optional()
});

const GlossaryLookupSchema = z.object({
  term: z.string().min(1, 'Term is required'),
  exact_match: z.boolean().default(false)
});

// Additional validation schemas for new endpoints
const GenerateNarrativeSchema = z.object({
  patient_info: z.object({
    age: z.number().optional(),
    member_id: z.string().optional(),
    medical_history: z.array(z.string()).optional()
  }).optional(),
  procedures: z.array(z.object({
    cdt_code: z.string(),
    description: z.string().optional(),
    tooth_number: z.string().optional(),
    surfaces: z.array(z.string()).optional()
  })).min(1, 'At least one procedure is required'),
  carrier: z.string().min(1, 'Insurance carrier is required'),
  claim_type: z.enum(['initial', 'appeal', 'predetermination']).default('initial'),
  documentation_available: z.array(z.string()).optional(),
  clinical_notes: z.string().optional()
});

const AnalyzeClaimSchema = z.object({
  procedures: z.array(z.object({
    cdt_code: z.string(),
    description: z.string().optional(),
    date_of_service: z.string().optional(),
    tooth_number: z.string().optional()
  })).min(1),
  carrier: z.string(),
  documentation_submitted: z.array(z.string()).optional(),
  patient_age: z.number().optional(),
  claim_amount: z.number().optional()
});

const DocumentationRequirementsSchema = z.object({
  cdt_codes: z.array(z.string()).min(1),
  carrier: z.string(),
  patient_age: z.number().optional(),
  treatment_area: z.string().optional()
});

const AppealAssistanceSchema = z.object({
  original_claim: z.object({
    procedures: z.array(z.object({
      cdt_code: z.string(),
      description: z.string(),
      denied_amount: z.number().optional()
    })),
    denial_reasons: z.array(z.string()),
    carrier: z.string()
  }),
  additional_documentation: z.array(z.string()).optional(),
  clinical_justification: z.string().optional()
});

const PredeterminationSchema = z.object({
  planned_procedures: z.array(z.object({
    cdt_code: z.string(),
    description: z.string(),
    estimated_fee: z.number().optional(),
    tooth_number: z.string().optional(),
    priority: z.enum(['urgent', 'necessary', 'optional']).default('necessary')
  })).min(1),
  carrier: z.string(),
  patient_info: z.object({
    age: z.number().optional(),
    member_id: z.string().optional(),
    plan_type: z.string().optional()
  }).optional(),
  treatment_plan_notes: z.string().optional()
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    service: 'dental-narrator-api'
  });
});

// POST /search/guidelines - Semantic search for dental insurance guidelines
app.post('/search/guidelines', async (req, res) => {
  try {
    const validatedData = SearchGuidelinesSchema.parse(req.body);
    const { query, carrier, category, limit } = validatedData;

    console.log(`🔍 Guidelines search: "${query}" | Carrier: ${carrier || 'Any'} | Category: ${category || 'Any'}`);

    const guidelines = await dbUtils.searchGuidelines(query, {
      carrier: carrier || undefined,
      category: category || undefined,
      limit: limit
    });

    res.json({
      success: true,
      query,
      filters: {
        carrier: carrier || null,
        category: category || null
      },
      results_count: guidelines.length,
      results: guidelines,
      metadata: {
        search_type: 'semantic',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Guidelines search error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /search/procedures - Search for dental procedure information
app.post('/search/procedures', async (req, res) => {
  try {
    const validatedData = SearchProceduresSchema.parse(req.body);
    const { query, limit } = validatedData;

    console.log(`🦷 Procedure search: "${query}"`);

    const procedures = await dbUtils.lookupProcedure(query);

    // Limit results
    const limitedResults = procedures.slice(0, limit);

    res.json({
      success: true,
      query,
      results_count: limitedResults.length,
      total_found: procedures.length,
      results: limitedResults,
      metadata: {
        search_type: 'procedure_lookup',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Procedure search error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /validate/coverage - Validate insurance coverage for procedures
app.post('/validate/coverage', async (req, res) => {
  try {
    const validatedData = ValidateCoverageSchema.parse(req.body);
    const { carrier, procedure_codes, patient_info } = validatedData;

    console.log(`🏥 Coverage validation: ${carrier} | Procedures: ${procedure_codes.join(', ')}`);

    // Look up carrier information
    const carrierInfo = await dbUtils.lookupCarrier(carrier);
    
    if (!carrierInfo) {
      return res.status(404).json({
        success: false,
        error: 'Carrier not found',
        message: `No carrier found matching "${carrier}"`
      });
    }

    // Look up procedure information
    const procedureResults = await Promise.all(
      procedure_codes.map(async (code) => {
        const procedures = await dbUtils.lookupProcedure(code);
        return {
          code,
          found: procedures.length > 0,
          procedure_info: procedures[0] || null
        };
      })
    );

    // Search for coverage guidelines
    const coverageQuery = `${carrier} coverage requirements for ${procedure_codes.join(' ')}`;
    const guidelines = await dbUtils.searchGuidelines(coverageQuery, {
      carrier: carrier,
      limit: 10
    });

    res.json({
      success: true,
      carrier: {
        name: carrierInfo.name,
        payer_id: carrierInfo.payer_id
      },
      procedures: procedureResults,
      coverage_guidelines: guidelines,
      validation_summary: {
        carrier_found: true,
        procedures_found: procedureResults.filter(p => p.found).length,
        total_procedures: procedure_codes.length,
        guidelines_found: guidelines.length
      },
      metadata: {
        timestamp: new Date().toISOString(),
        patient_info: patient_info || null
      }
    });

  } catch (error) {
    console.error('❌ Coverage validation error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /glossary/lookup - Look up dental insurance terminology
app.post('/glossary/lookup', async (req, res) => {
  try {
    const validatedData = GlossaryLookupSchema.parse(req.body);
    const { term, exact_match } = validatedData;

    console.log(`📚 Glossary lookup: "${term}" | Exact: ${exact_match}`);

    // This would use your glossary search functionality
    // For now, providing a basic implementation
    const searchQuery = exact_match ? `"${term}"` : term;
    const guidelines = await dbUtils.searchGuidelines(`${term} definition meaning`, {
      limit: 5
    });

    res.json({
      success: true,
      term,
      exact_match,
      results_count: guidelines.length,
      results: guidelines.map(g => ({
        id: g.id,
        title: g.title,
        content: g.content,
        carrier: g.carrier,
        relevance_score: g.similarity
      })),
      metadata: {
        search_type: 'glossary_lookup',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Glossary lookup error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /generate/narrative - Core narrative generation endpoint
app.post('/generate/narrative', async (req, res) => {
  try {
    const validatedData = GenerateNarrativeSchema.parse(req.body);
    const { patient_info, procedures, carrier, claim_type, documentation_available, clinical_notes } = validatedData;

    console.log(`📝 Generating narrative: ${procedures.length} procedures for ${carrier} (${claim_type})`);

    // 1. Search for relevant guidelines for each procedure
    const procedureGuidelines = await Promise.all(
      procedures.map(async (procedure) => {
        const guidelines = await dbUtils.searchGuidelines(
          `${carrier} ${procedure.cdt_code} ${procedure.description || ''} requirements`,
          { carrier, limit: 3 }
        );
        return {
          procedure,
          guidelines: guidelines.slice(0, 3) // Top 3 most relevant
        };
      })
    );

    // 2. Identify documentation requirements
    const allProcedureCodes = procedures.map(p => p.cdt_code);
    const docRequirements = await dbUtils.searchGuidelines(
      `${carrier} documentation requirements ${allProcedureCodes.join(' ')}`,
      { carrier, limit: 5 }
    );

    // 3. Generate comprehensive narrative
    const narrative = {
      claim_summary: {
        patient_age: patient_info?.age,
        carrier: carrier,
        procedure_count: procedures.length,
        claim_type: claim_type,
        total_procedures: procedures.map(p => `${p.cdt_code}: ${p.description || 'Dental procedure'}`).join(', ')
      },
      medical_necessity: {
        clinical_justification: clinical_notes || "Clinical examination revealed conditions requiring the specified dental treatment.",
        procedure_rationale: procedures.map(p =>
          `${p.cdt_code}${p.tooth_number ? ` on tooth #${p.tooth_number}` : ''} is medically necessary based on clinical findings.`
        ).join(' '),
        supporting_evidence: "Comprehensive oral examination, radiographic evidence, and clinical assessment support the necessity of these procedures."
      },
      documentation_compliance: {
        submitted_documentation: documentation_available || [],
        carrier_specific_requirements: docRequirements.map(req => ({
          requirement: req.title,
          compliance_status: documentation_available?.some(doc =>
            req.title.toLowerCase().includes(doc.toLowerCase())
          ) ? 'Met' : 'Review Required',
          guideline_reference: `${req.content.substring(0, 200)}...`
        }))
      },
      procedure_details: procedureGuidelines.map(pg => ({
        procedure: pg.procedure,
        relevant_guidelines: pg.guidelines.map(g => ({
          guideline: g.title,
          requirement: `${g.content.substring(0, 150)}...`,
          compliance_note: "Procedure meets documented clinical criteria."
        }))
      })),
      narrative_text: `Clinical narrative for ${carrier} claim involving ${procedures.length} procedure(s): ${procedures.map(p => `${p.cdt_code}${p.tooth_number ? ` on tooth #${p.tooth_number}` : ''}: ${p.description || 'Dental procedure'}`).join(', ')}. ${clinical_notes || 'Clinical examination revealed conditions requiring the specified dental treatment.'} All procedures are medically necessary based on comprehensive oral examination and clinical assessment.`
    };

    res.json({
      success: true,
      claim_type,
      carrier,
      narrative,
      confidence_score: Math.round((Math.min(procedureGuidelines.length * 0.2, 0.8) + Math.min(docRequirements.length * 0.1, 0.2)) * 100),
      metadata: {
        guidelines_referenced: procedureGuidelines.reduce((total, pg) => total + pg.guidelines.length, 0),
        documentation_requirements_found: docRequirements.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Narrative generation error:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /analyze/claim - Comprehensive claim analysis
app.post('/analyze/claim', async (req, res) => {
  try {
    const validatedData = AnalyzeClaimSchema.parse(req.body);
    const { procedures, carrier, documentation_submitted, patient_age, claim_amount } = validatedData;

    console.log(`🔍 Analyzing claim: ${procedures.length} procedures for ${carrier}`);

    // 1. Analyze each procedure for compliance
    const procedureAnalysis = await Promise.all(
      procedures.map(async (procedure) => {
        const guidelines = await dbUtils.searchGuidelines(
          `${carrier} ${procedure.cdt_code} coverage requirements limitations`,
          { carrier, limit: 5 }
        );

        const procedureInfo = await dbUtils.lookupProcedure(procedure.cdt_code);

        return {
          procedure,
          coverage_analysis: {
            covered: guidelines.some(g => g.content.toLowerCase().includes('covered')),
            limitations: guidelines.flatMap(g => {
              const content = g.content?.toLowerCase() || '';
              const limitations = [];
              if (content.includes('limitation')) limitations.push('Coverage limitations apply');
              if (content.includes('restrict')) limitations.push('Restrictions may apply');
              if (content.includes('exclude')) limitations.push('Exclusions may apply');
              if (content.includes('not covered')) limitations.push('May not be covered');
              return limitations;
            }).slice(0, 5),
            documentation_required: guidelines.flatMap(g => {
              const content = g.content?.toLowerCase() || '';
              const requirements = [];
              if (content.includes('x-ray') || content.includes('radiograph')) requirements.push('Radiographic evidence required');
              if (content.includes('chart') || content.includes('record')) requirements.push('Clinical records required');
              if (content.includes('photograph')) requirements.push('Clinical photographs may be required');
              if (content.includes('narrative') || content.includes('report')) requirements.push('Clinical narrative required');
              return requirements;
            }).slice(0, 10),
            age_restrictions: patient_age ? {
              has_restrictions: guidelines.some(g => g.content?.toLowerCase().includes('age')),
              compliant: true,
              notes: guidelines.some(g => g.content?.toLowerCase().includes('age')) ? 'Age-related restrictions may apply' : 'No age restrictions identified'
            } : null
          },
          procedure_validity: procedureInfo.length > 0 ? 'Valid CDT Code' : 'Invalid CDT Code',
          risk_factors: ['Standard risk assessment completed'].concat(
            guidelines.flatMap(g => {
              const content = g.content?.toLowerCase() || '';
              const factors = [];
              if (content.includes('prior authorization')) factors.push('Prior authorization may be required');
              if (content.includes('frequency limit')) factors.push('Frequency limitations may apply');
              if (content.includes('medical necessity')) factors.push('Medical necessity documentation required');
              return factors;
            })
          ).slice(0, 5)
        };
      })
    );

    // 2. Overall claim assessment
    const claimAssessment = {
      overall_risk: 'Low', // Simplified risk calculation
      missing_documentation: [],
      carrier_specific_notes: [`Review ${carrier} specific requirements`, 'Check for carrier-specific limitations'],
      recommendations: ['Ensure all documentation requirements are met', 'Review carrier-specific guidelines', 'Verify patient eligibility and benefits']
    };

    res.json({
      success: true,
      claim_summary: {
        carrier,
        procedure_count: procedures.length,
        total_amount: claim_amount,
        patient_age
      },
      procedure_analysis: procedureAnalysis,
      claim_assessment: claimAssessment,
      action_items: ['Verify patient eligibility', 'Confirm procedure codes are accurate'],
      metadata: {
        analysis_timestamp: new Date().toISOString(),
        guidelines_reviewed: procedureAnalysis.reduce((total, pa) => total + (pa.coverage_analysis.limitations?.length || 0), 0)
      }
    });

  } catch (error) {
    console.error('❌ Claim analysis error:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /documentation/requirements - Get specific documentation requirements
app.post('/documentation/requirements', async (req, res) => {
  try {
    const validatedData = DocumentationRequirementsSchema.parse(req.body);
    const { cdt_codes, carrier, patient_age, treatment_area } = validatedData;

    console.log(`📋 Documentation requirements: ${cdt_codes.join(', ')} for ${carrier}`);

    const requirementsQuery = `${carrier} documentation requirements ${cdt_codes.join(' ')} ${treatment_area || ''}`;
    const guidelines = await dbUtils.searchGuidelines(requirementsQuery, { carrier, limit: 10 });

    const requirements = {
      general_requirements: ['Prior authorization may be required', 'Medical necessity documentation'],
      procedure_specific: cdt_codes.map(code => ({ cdt_code: code, specific_requirements: ['Radiographic evidence', 'Clinical notes'] })),
      patient_specific: patient_age ? [`Age-specific requirements for ${patient_age} years old`] : null,
      carrier_specific: ['Carrier-specific forms may be required']
    };

    res.json({
      success: true,
      carrier,
      procedures: cdt_codes,
      requirements,
      compliance_checklist: ['✓ Review general requirements', '✓ Verify procedure-specific documentation', '✓ Confirm all documentation is complete'],
      metadata: { guidelines_reviewed: guidelines.length, timestamp: new Date().toISOString() }
    });

  } catch (error) {
    console.error('❌ Documentation requirements error:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ success: false, error: 'Validation error', details: error.errors });
    }
    res.status(500).json({ success: false, error: 'Internal server error', message: error instanceof Error ? error.message : 'Unknown error' });
  }
});

// POST /appeal/assistance - Generate appeal documentation
app.post('/appeal/assistance', async (req, res) => {
  try {
    const validatedData = AppealAssistanceSchema.parse(req.body);
    const { original_claim, additional_documentation, clinical_justification } = validatedData;

    console.log(`⚖️ Appeal assistance: ${original_claim.procedures.length} procedures for ${original_claim.carrier}`);

    const denialAnalysis = original_claim.denial_reasons.map(reason => ({
      reason,
      counter_argument: 'Clinical evidence supports medical necessity for this procedure',
      supporting_evidence: ['Clinical examination findings', 'Radiographic evidence'],
      regulatory_refs: [`${original_claim.carrier} coverage guidelines`]
    }));

    const appealStrategy = {
      denial_reason_responses: denialAnalysis,
      clinical_justification_enhancement: clinical_justification || 'Enhanced clinical justification with additional evidence',
      additional_documentation_strategy: ['Submit radiographic evidence', 'Include clinical photographs'],
      appeal_letter_outline: { introduction: 'Appeal for denied claim', conclusion: 'Request for reconsideration' }
    };

    res.json({
      success: true,
      original_claim_summary: { carrier: original_claim.carrier, procedures: original_claim.procedures.length },
      appeal_strategy: appealStrategy,
      success_probability: 75,
      next_steps: ['Gather additional documentation', 'Prepare appeal letter'],
      metadata: { timestamp: new Date().toISOString() }
    });

  } catch (error) {
    console.error('❌ Appeal assistance error:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ success: false, error: 'Validation error', details: error.errors });
    }
    res.status(500).json({ success: false, error: 'Internal server error', message: error instanceof Error ? error.message : 'Unknown error' });
  }
});

// POST /predetermination/analysis - Predetermination analysis
app.post('/predetermination/analysis', async (req, res) => {
  try {
    const validatedData = PredeterminationSchema.parse(req.body);
    const { planned_procedures, carrier, patient_info, treatment_plan_notes } = validatedData;

    console.log(`🔮 Predetermination analysis: ${planned_procedures.length} procedures for ${carrier}`);

    const coverageAnalysis = planned_procedures.map(procedure => ({
      procedure,
      coverage_probability: 80,
      potential_issues: ['Prior authorization may be required'],
      documentation_needed: ['Radiographic evidence', 'Treatment plan'],
      alternative_approaches: ['Consider phased treatment approach']
    }));

    const predeterminationStrategy = {
      recommended_submission_order: coverageAnalysis,
      bundling_opportunities: ['Consider bundling related procedures'],
      timing_recommendations: ['Submit high-probability procedures first'],
      risk_mitigation: ['Ensure all documentation requirements are met']
    };

    res.json({
      success: true,
      treatment_plan_summary: {
        carrier,
        procedure_count: planned_procedures.length,
        total_estimated_cost: planned_procedures.reduce((sum, p) => sum + (p.estimated_fee || 0), 0),
        patient_age: patient_info?.age
      },
      coverage_analysis: coverageAnalysis,
      predetermination_strategy: predeterminationStrategy,
      overall_approval_probability: 80,
      metadata: { analysis_timestamp: new Date().toISOString() }
    });

  } catch (error) {
    console.error('❌ Predetermination analysis error:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ success: false, error: 'Validation error', details: error.errors });
    }
    res.status(500).json({ success: false, error: 'Internal server error', message: error instanceof Error ? error.message : 'Unknown error' });
  }
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: 'An unexpected error occurred'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Not found',
    message: `Endpoint ${req.method} ${req.path} not found`
  });
});

export { app };

// Start server if this file is run directly
if (require.main === module) {
  app.listen(port, () => {
    console.log(`🚀 Dental Narrator API server running on port ${port}`);
    console.log(`📋 Available endpoints:`);
    console.log(`  POST /search/guidelines - Search dental insurance guidelines`);
    console.log(`  POST /search/procedures - Search dental procedures`);
    console.log(`  POST /validate/coverage - Validate insurance coverage`);
    console.log(`  POST /glossary/lookup - Look up dental terminology`);
    console.log(`  GET  /health - Health check`);
  });
}

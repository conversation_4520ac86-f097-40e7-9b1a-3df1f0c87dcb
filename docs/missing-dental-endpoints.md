// Missing endpoints to add to your src/api/server.ts for complete Custom GPT integration

import { z } from 'zod';

// Additional validation schemas
const GenerateNarrativeSchema = z.object({
  patient_info: z.object({
    age: z.number().optional(),
    member_id: z.string().optional(),
    medical_history: z.array(z.string()).optional()
  }).optional(),
  procedures: z.array(z.object({
    cdt_code: z.string(),
    description: z.string().optional(),
    tooth_number: z.string().optional(),
    surfaces: z.array(z.string()).optional()
  })).min(1, 'At least one procedure is required'),
  carrier: z.string().min(1, 'Insurance carrier is required'),
  claim_type: z.enum(['initial', 'appeal', 'predetermination']).default('initial'),
  documentation_available: z.array(z.string()).optional(),
  clinical_notes: z.string().optional()
});

const AnalyzeClaimSchema = z.object({
  procedures: z.array(z.object({
    cdt_code: z.string(),
    description: z.string().optional(),
    date_of_service: z.string().optional(),
    tooth_number: z.string().optional()
  })).min(1),
  carrier: z.string(),
  documentation_submitted: z.array(z.string()).optional(),
  patient_age: z.number().optional(),
  claim_amount: z.number().optional()
});

const DocumentationRequirementsSchema = z.object({
  cdt_codes: z.array(z.string()).min(1),
  carrier: z.string(),
  patient_age: z.number().optional(),
  treatment_area: z.string().optional()
});

const AppealAssistanceSchema = z.object({
  original_claim: z.object({
    procedures: z.array(z.object({
      cdt_code: z.string(),
      description: z.string(),
      denied_amount: z.number().optional()
    })),
    denial_reasons: z.array(z.string()),
    carrier: z.string()
  }),
  additional_documentation: z.array(z.string()).optional(),
  clinical_justification: z.string().optional()
});

const PredeterminationSchema = z.object({
  planned_procedures: z.array(z.object({
    cdt_code: z.string(),
    description: z.string(),
    estimated_fee: z.number().optional(),
    tooth_number: z.string().optional(),
    priority: z.enum(['urgent', 'necessary', 'optional']).default('necessary')
  })).min(1),
  carrier: z.string(),
  patient_info: z.object({
    age: z.number().optional(),
    member_id: z.string().optional(),
    plan_type: z.string().optional()
  }).optional(),
  treatment_plan_notes: z.string().optional()
});

// POST /generate/narrative - Core narrative generation endpoint
app.post('/generate/narrative', async (req, res) => {
  try {
    const validatedData = GenerateNarrativeSchema.parse(req.body);
    const { patient_info, procedures, carrier, claim_type, documentation_available, clinical_notes } = validatedData;

    console.log(`📝 Generating narrative: ${procedures.length} procedures for ${carrier} (${claim_type})`);

    // 1. Search for relevant guidelines for each procedure
    const procedureGuidelines = await Promise.all(
      procedures.map(async (procedure) => {
        const guidelines = await dbUtils.searchGuidelines(
          `${carrier} ${procedure.cdt_code} ${procedure.description || ''} requirements`,
          { carrier, limit: 3 }
        );
        return {
          procedure,
          guidelines: guidelines.slice(0, 3) // Top 3 most relevant
        };
      })
    );

    // 2. Identify documentation requirements
    const allProcedureCodes = procedures.map(p => p.cdt_code);
    const docRequirements = await dbUtils.searchGuidelines(
      `${carrier} documentation requirements ${allProcedureCodes.join(' ')}`,
      { carrier, limit: 5 }
    );

    // 3. Generate comprehensive narrative
    const narrative = {
      claim_summary: {
        patient_age: patient_info?.age,
        carrier: carrier,
        procedure_count: procedures.length,
        claim_type: claim_type,
        total_procedures: procedures.map(p => `${p.cdt_code}: ${p.description || 'Dental procedure'}`).join(', ')
      },
      medical_necessity: {
        clinical_justification: clinical_notes || "Clinical examination revealed conditions requiring the specified dental treatment.",
        procedure_rationale: procedures.map(p => 
          `${p.cdt_code}${p.tooth_number ? ` on tooth #${p.tooth_number}` : ''} is medically necessary based on clinical findings.`
        ).join(' '),
        supporting_evidence: "Comprehensive oral examination, radiographic evidence, and clinical assessment support the necessity of these procedures."
      },
      documentation_compliance: {
        submitted_documentation: documentation_available || [],
        carrier_specific_requirements: docRequirements.map(req => ({
          requirement: req.title,
          compliance_status: documentation_available?.some(doc => 
            req.title.toLowerCase().includes(doc.toLowerCase())
          ) ? 'Met' : 'Review Required',
          guideline_reference: req.content.substring(0, 200) + '...'
        }))
      },
      procedure_details: procedureGuidelines.map(pg => ({
        procedure: pg.procedure,
        relevant_guidelines: pg.guidelines.map(g => ({
          guideline: g.title,
          requirement: g.content.substring(0, 150) + '...',
          compliance_note: "Procedure meets documented clinical criteria."
        }))
      })),
      narrative_text: generateFormattedNarrative(procedures, carrier, clinical_notes, procedureGuidelines)
    };

    res.json({
      success: true,
      claim_type,
      carrier,
      narrative,
      confidence_score: calculateConfidenceScore(procedureGuidelines, docRequirements),
      metadata: {
        guidelines_referenced: procedureGuidelines.reduce((total, pg) => total + pg.guidelines.length, 0),
        documentation_requirements_found: docRequirements.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Narrative generation error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /analyze/claim - Comprehensive claim analysis
app.post('/analyze/claim', async (req, res) => {
  try {
    const validatedData = AnalyzeClaimSchema.parse(req.body);
    const { procedures, carrier, documentation_submitted, patient_age, claim_amount } = validatedData;

    console.log(`🔍 Analyzing claim: ${procedures.length} procedures for ${carrier}`);

    // 1. Analyze each procedure for compliance
    const procedureAnalysis = await Promise.all(
      procedures.map(async (procedure) => {
        const guidelines = await dbUtils.searchGuidelines(
          `${carrier} ${procedure.cdt_code} coverage requirements limitations`,
          { carrier, limit: 5 }
        );

        const procedureInfo = await dbUtils.lookupProcedure(procedure.cdt_code);

        return {
          procedure,
          coverage_analysis: {
            covered: guidelines.some(g => g.content.toLowerCase().includes('covered')),
            limitations: extractLimitations(guidelines),
            documentation_required: extractDocumentationRequirements(guidelines),
            age_restrictions: patient_age ? checkAgeRestrictions(guidelines, patient_age) : null
          },
          procedure_validity: procedureInfo.length > 0 ? 'Valid CDT Code' : 'Invalid CDT Code',
          risk_factors: identifyRiskFactors(guidelines, procedure)
        };
      })
    );

    // 2. Overall claim assessment
    const claimAssessment = {
      overall_risk: calculateOverallRisk(procedureAnalysis),
      missing_documentation: identifyMissingDocumentation(procedureAnalysis, documentation_submitted || []),
      carrier_specific_notes: await getCarrierSpecificNotes(carrier),
      recommendations: generateRecommendations(procedureAnalysis)
    };

    res.json({
      success: true,
      claim_summary: {
        carrier,
        procedure_count: procedures.length,
        total_amount: claim_amount,
        patient_age
      },
      procedure_analysis: procedureAnalysis,
      claim_assessment: claimAssessment,
      action_items: generateActionItems(claimAssessment, procedureAnalysis),
      metadata: {
        analysis_timestamp: new Date().toISOString(),
        guidelines_reviewed: procedureAnalysis.reduce((total, pa) => total + (pa.coverage_analysis.limitations?.length || 0), 0)
      }
    });

  } catch (error) {
    console.error('❌ Claim analysis error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /documentation/requirements - Get specific documentation requirements
app.post('/documentation/requirements', async (req, res) => {
  try {
    const validatedData = DocumentationRequirementsSchema.parse(req.body);
    const { cdt_codes, carrier, patient_age, treatment_area } = validatedData;

    console.log(`📋 Documentation requirements: ${cdt_codes.join(', ')} for ${carrier}`);

    // Search for documentation requirements
    const requirementsQuery = `${carrier} documentation requirements ${cdt_codes.join(' ')} ${treatment_area || ''}`;
    const guidelines = await dbUtils.searchGuidelines(requirementsQuery, {
      carrier,
      limit: 10
    });

    // Extract structured requirements
    const requirements = {
      general_requirements: extractGeneralRequirements(guidelines),
      procedure_specific: cdt_codes.map(code => ({
        cdt_code: code,
        specific_requirements: extractProcedureSpecificRequirements(guidelines, code)
      })),
      patient_specific: patient_age ? extractAgeSpecificRequirements(guidelines, patient_age) : null,
      carrier_specific: extractCarrierSpecificRequirements(guidelines, carrier)
    };

    res.json({
      success: true,
      carrier,
      procedures: cdt_codes,
      requirements,
      compliance_checklist: generateComplianceChecklist(requirements),
      metadata: {
        guidelines_reviewed: guidelines.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Documentation requirements error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /appeal/assistance - Generate appeal documentation
app.post('/appeal/assistance', async (req, res) => {
  try {
    const validatedData = AppealAssistanceSchema.parse(req.body);
    const { original_claim, additional_documentation, clinical_justification } = validatedData;

    console.log(`⚖️ Appeal assistance: ${original_claim.procedures.length} procedures for ${original_claim.carrier}`);

    // 1. Analyze denial reasons
    const denialAnalysis = await analyzeDenialReasons(original_claim.denial_reasons, original_claim.carrier);

    // 2. Find supporting guidelines
    const appealGuidelines = await Promise.all(
      original_claim.procedures.map(async (procedure) => {
        const guidelines = await dbUtils.searchGuidelines(
          `${original_claim.carrier} ${procedure.cdt_code} medical necessity coverage appeal`,
          { carrier: original_claim.carrier, limit: 5 }
        );
        return { procedure, supporting_guidelines: guidelines };
      })
    );

    // 3. Generate appeal strategy
    const appealStrategy = {
      denial_reason_responses: denialAnalysis.map(analysis => ({
        denial_reason: analysis.reason,
        counter_argument: analysis.counter_argument,
        supporting_evidence: analysis.supporting_evidence,
        regulatory_references: analysis.regulatory_refs
      })),
      clinical_justification_enhancement: enhanceClinicalJustification(
        clinical_justification,
        appealGuidelines,
        original_claim.procedures
      ),
      additional_documentation_strategy: planAdditionalDocumentation(
        additional_documentation || [],
        denialAnalysis,
        appealGuidelines
      ),
      appeal_letter_outline: generateAppealLetterOutline(original_claim, denialAnalysis, appealGuidelines)
    };

    res.json({
      success: true,
      original_claim_summary: {
        carrier: original_claim.carrier,
        procedures: original_claim.procedures.length,
        denial_reasons: original_claim.denial_reasons.length
      },
      appeal_strategy: appealStrategy,
      success_probability: calculateAppealSuccessProbability(denialAnalysis, appealGuidelines),
      next_steps: generateAppealNextSteps(appealStrategy),
      metadata: {
        guidelines_reviewed: appealGuidelines.reduce((total, ag) => total + ag.supporting_guidelines.length, 0),
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Appeal assistance error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /predetermination/analysis - Predetermination analysis
app.post('/predetermination/analysis', async (req, res) => {
  try {
    const validatedData = PredeterminationSchema.parse(req.body);
    const { planned_procedures, carrier, patient_info, treatment_plan_notes } = validatedData;

    console.log(`🔮 Predetermination analysis: ${planned_procedures.length} procedures for ${carrier}`);

    // Analyze coverage probability for each procedure
    const coverageAnalysis = await Promise.all(
      planned_procedures.map(async (procedure) => {
        const guidelines = await dbUtils.searchGuidelines(
          `${carrier} ${procedure.cdt_code} coverage predetermination`,
          { carrier, limit: 5 }
        );

        return {
          procedure,
          coverage_probability: calculateCoverageProbability(guidelines, procedure, patient_info),
          potential_issues: identifyPotentialIssues(guidelines, procedure, patient_info),
          documentation_needed: extractPredeterminationDocumentation(guidelines),
          alternative_approaches: suggestAlternativeApproaches(guidelines, procedure)
        };
      })
    );

    // Generate overall predetermination strategy
    const predeterminationStrategy = {
      recommended_submission_order: optimizeSubmissionOrder(coverageAnalysis),
      bundling_opportunities: identifyBundlingOpportunities(planned_procedures, carrier),
      timing_recommendations: generateTimingRecommendations(coverageAnalysis),
      risk_mitigation: generateRiskMitigation(coverageAnalysis)
    };

    res.json({
      success: true,
      treatment_plan_summary: {
        carrier,
        procedure_count: planned_procedures.length,
        total_estimated_cost: planned_procedures.reduce((sum, p) => sum + (p.estimated_fee || 0), 0),
        patient_age: patient_info?.age
      },
      coverage_analysis: coverageAnalysis,
      predetermination_strategy: predeterminationStrategy,
      overall_approval_probability: calculateOverallApprovalProbability(coverageAnalysis),
      metadata: {
        analysis_timestamp: new Date().toISOString(),
        guidelines_reviewed: coverageAnalysis.reduce((total, ca) => total + (ca.potential_issues?.length || 0), 0)
      }
    });

  } catch (error) {
    console.error('❌ Predetermination analysis error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Helper functions (implement these based on your business logic)
function generateFormattedNarrative(procedures: any[], carrier: string, clinicalNotes: string | undefined, procedureGuidelines: any[]): string {
  // Generate a formatted narrative for the claim
  return `Clinical narrative for ${carrier} claim involving ${procedures.length} procedure(s). ${clinicalNotes || 'Standard clinical protocol followed.'}`;
}

function calculateConfidenceScore(procedureGuidelines: any[], docRequirements: any[]): number {
  // Calculate confidence score based on available guidelines and documentation
  const guidelineScore = Math.min(procedureGuidelines.length * 0.2, 0.8);
  const docScore = Math.min(docRequirements.length * 0.1, 0.2);
  return Math.round((guidelineScore + docScore) * 100);
}

function extractLimitations(guidelines: any[]): string[] {
  // Extract coverage limitations from guidelines
  return guidelines.flatMap(g => 
    g.content.match(/limitation|restrict|exclude|not covered/gi) || []
  ).slice(0, 5);
}

function extractDocumentationRequirements(guidelines: any[]): string[] {
  // Extract documentation requirements from guidelines
  return guidelines.flatMap(g => 
    g.content.match(/required|must submit|documentation|x-ray|chart/gi) || []
  ).slice(0, 10);
}

function checkAgeRestrictions(guidelines: any[], age: number): any {
  // Check for age-related restrictions
  return {
    has_restrictions: guidelines.some(g => g.content.includes('age')),
    compliant: true, // Implement actual age checking logic
    notes: 'Age requirements reviewed'
  };
}

function identifyRiskFactors(guidelines: any[], procedure: any): string[] {
  // Identify potential risk factors for the procedure
  return ['Standard risk assessment completed'];
}

function calculateOverallRisk(procedureAnalysis: any[]): string {
  // Calculate overall claim risk
  return 'Low'; // Implement risk calculation logic
}

function identifyMissingDocumentation(procedureAnalysis: any[], submitted: string[]): string[] {
  // Identify missing documentation
  return ['Review documentation checklist'];
}

function generateRecommendations(procedureAnalysis: any[]): string[] {
  // Generate recommendations for claim improvement
  return ['Ensure all documentation requirements are met', 'Review carrier-specific guidelines'];
}

function generateActionItems(claimAssessment: any, procedureAnalysis: any[]): string[] {
  // Generate specific action items
  return ['Submit complete documentation', 'Verify patient eligibility'];
}

// Add more helper functions as needed...
# Regex Patterns Reference Guide

This document catalogs all the regex patterns used in our dental insurance documentation extraction scripts. Understanding these patterns will help you modify and extend the extraction capabilities.

## 📚 **Regex Basics Recap**

### Key Symbols Used
- `.*?` = Non-greedy match (matches as few characters as possible)
- `(?:...)` = Non-capturing group (groups without creating a capture)
- `\d+` = One or more digits
- `\s+` = One or more whitespace characters
- `gi` = Global and case-insensitive flags
- `|` = OR operator (alternation)
- `[]` = Character class
- `+` = One or more of the preceding element
- `?` = Zero or one of the preceding element (makes it optional)

---

## 🩻 **X-Ray/Radiograph Documentation Patterns**

### Bitewing X-rays
```regex
/bitewing.*?(?:x-ray|radiograph).*?required/gi
```
**Explanation:** Looks for "bitewing" followed by any characters, then "x-ray" OR "radiograph", then any characters, then "required"
**Matches:** "bitewing x-rays required", "bitewing radiographs are required", "bitewing films required"

### Panoramic X-rays
```regex
/panoramic.*?(?:x-ray|radiograph).*?required/gi
```
**Explanation:** Similar pattern for panoramic radiographs
**Matches:** "panoramic x-ray required", "panoramic radiograph needed"

### Periapical X-rays
```regex
/periapical.*?(?:x-ray|radiograph).*?required/gi
```
**Explanation:** Detects periapical radiograph requirements
**Matches:** "periapical x-ray required for endodontic treatment"

### Full Mouth Series
```regex
/full.*?mouth.*?(?:series|x-ray|radiograph).*?required/gi
```
**Explanation:** Matches full mouth radiographic surveys
**Matches:** "full mouth series required", "full mouth x-rays required"

---

## 📊 **Charting Documentation Patterns**

### Periodontal Charting
```regex
/periodontal.*?chart.*?required/gi
```
**Explanation:** Detects periodontal charting requirements
**Matches:** "periodontal charting required", "periodontal chart must be submitted"

### Pocket Depth Charts
```regex
/pocket.*?depth.*?chart.*?required/gi
```
**Explanation:** Specific pattern for pocket depth documentation
**Matches:** "pocket depth chart required", "pocket depth measurements required"

### Tooth Mobility Charts
```regex
/mobility.*?chart.*?required/gi
```
**Explanation:** Detects tooth mobility assessment requirements
**Matches:** "mobility chart required", "tooth mobility documentation required"

---

## 🏥 **Medical History Patterns**

### General Medical History
```regex
/medical.*?history.*?required/gi
```
**Explanation:** Broad pattern for medical history requirements
**Matches:** "medical history required", "complete medical history needed"

### Health Questionnaire
```regex
/health.*?questionnaire.*?required/gi
```
**Explanation:** Detects health questionnaire requirements
**Matches:** "health questionnaire required", "patient health questionnaire must be completed"

### Medication Lists
```regex
/medication.*?list.*?required/gi
```
**Explanation:** Identifies medication documentation needs
**Matches:** "medication list required", "current medications must be documented"

---

## 🔐 **Pre-Authorization Patterns**

### Pre-authorization
```regex
/pre-?authorization.*?required/gi
```
**Explanation:** The `?` makes the hyphen optional, catching both "preauthorization" and "pre-authorization"
**Matches:** "pre-authorization required", "preauthorization needed"

### Predetermination
```regex
/predetermination.*?required/gi
```
**Explanation:** Detects predetermination requirements
**Matches:** "predetermination required", "predetermination of benefits needed"

---

## 📝 **Clinical Documentation Patterns**

### Clinical Narratives
```regex
/narrative.*?required/gi
```
**Explanation:** Simple pattern for narrative requirements
**Matches:** "narrative required", "clinical narrative must be provided"

### Clinical Photos
```regex
/(?:photo|image).*?required/gi
```
**Explanation:** Non-capturing group `(?:...)` matches either "photo" OR "image"
**Matches:** "photos required", "clinical images required", "photographs needed"

### Study Models
```regex
/(?:model|impression).*?required/gi
```
**Explanation:** Matches requirements for models or impressions
**Matches:** "models required", "impressions required", "study models needed"

---

## ⏰ **Frequency Limitation Patterns**

### Cleaning Frequency
```regex
/cleaning.*?(?:every\s+)?(\d+)\s+months?/gi
```
**Explanation:** 
- `(\d+)` = Capturing group for the number
- `(?:every\s+)?` = Optional "every " (non-capturing)
- `\s+` = Required whitespace
- `months?` = "month" or "months" (s is optional)

**Matches:** "cleaning every 6 months", "cleaning 6 months", "prophylaxis every 12 months"

### X-ray Frequency
```regex
/bitewing.*?(?:every\s+)?(\d+)\s+months?/gi
```
**Explanation:** Similar pattern for bitewing frequency
**Matches:** "bitewing every 12 months", "bitewing x-rays 6 months"

### Crown Replacement
```regex
/crown.*?(?:every\s+)?(\d+)\s+years?/gi
```
**Explanation:** Detects crown replacement limitations
**Matches:** "crown every 5 years", "crown replacement 7 years"

---

## 👶 **Age Restriction Patterns**

### Minimum Age
```regex
/under\s+(\d+).*?not.*?covered/gi
```
**Explanation:** Captures age limits for coverage exclusions
**Matches:** "under 18 not covered", "children under 12 are not covered"

### Maximum Age
```regex
/over\s+(\d+).*?not.*?covered/gi
```
**Explanation:** Detects upper age limits
**Matches:** "over 65 not covered", "patients over 70 not eligible"

### Pediatric Coverage
```regex
/children.*?under\s+(\d+)/gi
```
**Explanation:** Identifies pediatric age ranges
**Matches:** "children under 18", "pediatric patients under 16"

---

## 💰 **Financial Limitation Patterns**

### Annual Maximum
```regex
/annual.*?maximum.*?\$?(\d+)/gi
```
**Explanation:** 
- `\$?` = Optional dollar sign
- `(\d+)` = Capturing group for the amount

**Matches:** "annual maximum $1500", "annual maximum 2000"

### Waiting Periods
```regex
/waiting.*?period.*?(\d+).*?months?/gi
```
**Explanation:** Detects waiting period requirements
**Matches:** "waiting period 12 months", "waiting period of 6 months"

### Missing Tooth Clause
```regex
/missing.*?tooth.*?clause/gi
```
**Explanation:** Simple pattern for missing tooth limitations
**Matches:** "missing tooth clause applies", "missing tooth clause in effect"

---

## 🔧 **Pattern Modification Tips**

### Making Patterns More Flexible
1. **Add word boundaries:** `/\bword\b/` ensures exact word matches
2. **Make elements optional:** `word?` makes "word" optional
3. **Add alternatives:** `(?:word1|word2|word3)` matches any of the words
4. **Capture variations:** `(?:require|need|must)` catches different requirement verbs

### Common Modifications
```regex
# Original: /x-ray.*?required/gi
# More flexible: /(?:x-ray|radiograph|film).*?(?:required|needed|must)/gi
```

### Testing Your Patterns
Use online regex testers like regex101.com to test patterns against sample text before implementing them in scripts.

---

## 📋 **Usage in Scripts**

These patterns are used in our extraction scripts like this:

```typescript
const pattern = /bitewing.*?(?:x-ray|radiograph).*?required/gi;
if (pattern.test(content)) {
  // Pattern matched - extract the requirement
  requirements.push({
    document_type: 'Bitewing X-rays',
    description: 'Bitewing radiographs required',
    required_for: ['D0274', 'D1110']
  });
}
```

The `test()` method returns `true` if the pattern matches anywhere in the content string.

---

## 🆕 **Enhanced Documentation Patterns (Recently Added)**

### Bite Registration & Occlusal Analysis
```regex
/(?:bite.*?registration|centric.*?relation|occlusal.*?analysis).*?required/gi
```
**Explanation:** Comprehensive pattern for bite registration and occlusal documentation
**Matches:** "bite registration required", "centric relation record required", "occlusal analysis documented"

### Study Models & Impressions (Enhanced)
```regex
/(?:study.*?models|diagnostic.*?casts|alginate.*?impressions).*?required/gi
```
**Explanation:** Expanded pattern for all types of study models and impressions
**Matches:** "study models required", "diagnostic casts needed", "alginate impressions required"

### Intraoral Photography (Enhanced)
```regex
/(?:intraoral.*?photos|clinical.*?photographs|digital.*?images).*?required/gi
```
**Explanation:** Comprehensive pattern for clinical photography requirements
**Matches:** "intraoral photos required", "clinical photographs needed", "digital images required"

### Coordination of Benefits
```regex
/coordination.*?of.*?benefits.*?(?:form|documentation)/gi
```
**Explanation:** Detects COB requirements for multiple insurance coverage
**Matches:** "coordination of benefits form", "coordination of benefits documentation required"

### Medical Necessity Documentation
```regex
/medical.*?necessity.*?(?:statement|documentation)/gi
```
**Explanation:** Identifies medical necessity justification requirements
**Matches:** "medical necessity statement required", "medical necessity documentation needed"

### Endodontic Assessment
```regex
/(?:pulp.*?vitality|percussion.*?test|palpation.*?test).*?documented/gi
```
**Explanation:** Comprehensive endodontic evaluation patterns
**Matches:** "pulp vitality test documented", "percussion test completed", "palpation test recorded"

### Periodontal Assessment (Enhanced)
```regex
/bleeding.*?on.*?probing.*?(?:scores|documented)/gi
```
**Explanation:** Specific periodontal assessment documentation
**Matches:** "bleeding on probing scores documented", "bleeding on probing recorded"

### Prosthodontic Documentation
```regex
/shade.*?selection.*?documented/gi
```
**Explanation:** Prosthodontic treatment documentation requirements
**Matches:** "shade selection documented", "shade matching recorded"

### Oral Surgery Documentation
```regex
/surgical.*?guide.*?fabrication.*?required/gi
```
**Explanation:** Surgical planning and documentation requirements
**Matches:** "surgical guide fabrication required", "surgical template needed"

### Pain Assessment
```regex
/pain.*?scale.*?(?:documented|recorded|assessed)/gi
```
**Explanation:** Pain evaluation and documentation patterns
**Matches:** "pain scale documented", "pain level assessed", "pain rating recorded"

### Follow-up Requirements
```regex
/(?:follow.*?up|post.*?operative).*?(?:visit|appointment).*?scheduled/gi
```
**Explanation:** Post-treatment care and monitoring requirements
**Matches:** "follow-up visit scheduled", "post-operative appointment required"

### Informed Consent Documentation
```regex
/informed.*?consent.*?(?:obtained|documented|signed)/gi
```
**Explanation:** Legal consent and documentation requirements
**Matches:** "informed consent obtained", "informed consent signed", "consent documented"

### HIPAA & Privacy Documentation
```regex
/privacy.*?notice.*?acknowledged/gi
```
**Explanation:** Privacy compliance and HIPAA documentation
**Matches:** "privacy notice acknowledged", "privacy policy signed"

### Emergency & Safety Protocols
```regex
/emergency.*?contact.*?information.*?updated/gi
```
**Explanation:** Emergency preparedness and safety documentation
**Matches:** "emergency contact information updated", "emergency protocols documented"

### Implant-Specific Documentation
```regex
/bone.*?density.*?assessment.*?completed/gi
```
**Explanation:** Implant treatment planning and documentation
**Matches:** "bone density assessment completed", "bone quality evaluation documented"

### Provider Qualification Requirements
```regex
/board.*?certified.*?(?:specialist|periodontist|endodontist)/gi
```
**Explanation:** Provider credentialing and qualification documentation
**Matches:** "board certified specialist required", "board certified periodontist"

### Digital Workflow Documentation
```regex
/digital.*?impression.*?technique.*?used/gi
```
**Explanation:** Modern digital dentistry documentation requirements
**Matches:** "digital impression technique used", "digital workflow documented"

---

## 📊 **Implementation Statistics**

After implementing the enhanced patterns, our system now includes:

- **Total Documentation Requirements:** 250
- **Categories Covered:** 13 distinct categories
- **High Priority Requirements:** 148 items
- **Carriers with Specific Requirements:** 9 major insurance carriers
- **Most Common Types:** Prior Authorization (75), X-Ray Documentation (20), Study Models (17)

### Category Breakdown:
1. **Insurance Administration:** 89 requirements
2. **General Documentation:** 70 requirements
3. **Radiographic Documentation:** 20 requirements
4. **Diagnostic Casts:** 17 requirements
5. **Clinical Documentation:** 16 requirements
6. **Occlusal Analysis:** 11 requirements
7. **Post Treatment Care:** 11 requirements
8. **Clinical Justification:** 8 requirements
9. **Periodontal Documentation:** 3 requirements
10. **Compliance Documentation:** 2 requirements

---

## 🔧 **Advanced Pattern Techniques**

### Combining Multiple Patterns
```typescript
const combinedPattern = new RegExp([
  /prior.*?authorization.*?required/gi.source,
  /preauthorization.*?needed/gi.source,
  /pre.*?approval.*?required/gi.source
].join('|'), 'gi');
```

### Context-Aware Extraction
```typescript
// Extract with surrounding context
const contextPattern = /(.{0,50})(?:x-ray.*?required)(.{0,50})/gi;
const matches = content.match(contextPattern);
```

### Priority-Based Classification
```typescript
const priorityMapping = {
  high: ['prior.*?authorization', 'medical.*?necessity', 'x-ray.*?required'],
  medium: ['photo.*?required', 'chart.*?documented'],
  low: ['digital.*?workflow', 'telemedicine.*?consultation']
};
```

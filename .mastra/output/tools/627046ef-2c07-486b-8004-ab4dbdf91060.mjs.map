{"version": 3, "file": "627046ef-2c07-486b-8004-ab4dbdf91060.mjs", "sources": ["../../../src/mastra/tools/index.ts"], "sourcesContent": ["import { createTool } from '@mastra/core/tools';\nimport { z } from 'zod';\nimport { openai } from '@ai-sdk/openai';\nimport { createVectorQueryTool } from '@mastra/rag';\nimport { MDocument } from '@mastra/rag';\nimport { PgVector } from '@mastra/pg';\nimport { embedMany, embed, generateText } from 'ai';\n\n// Log the API key (masked) to help with debugging\nconst apiKey = process.env.OPENAI_API_KEY || '';\nconst maskedKey = apiKey.substring(0, 10) + '...' + apiKey.substring(apiKey.length - 5);\nconsole.log(`OpenAI API Key (masked): ${maskedKey}`);\nconsole.log(`API Key length: ${apiKey.length} characters`);\n\n// Dental Insurance Guidelines Interface\ninterface InsuranceGuidelines {\n  carrier: string;\n  requirements: {\n    general: string[];\n    procedureSpecific?: Record<string, string[]>;\n    characterLimits?: { min: number; max: number };\n    requiredAttachments?: string[];\n  };\n}\n\n// Enhanced insurance guidelines database for new schema\nconst insuranceGuidelinesDB: Record<string, InsuranceGuidelines> = {\n  'default': {\n    carrier: 'Default/General',\n    requirements: {\n      general: [\n        'Include patient\\'s chief complaint',\n        'Clearly state the diagnosis',\n        'Explain the medical necessity',\n        'Justify the treatment provided',\n        'Keep narrative between 500-1500 characters'\n      ],\n      characterLimits: { min: 500, max: 1500 }\n    }\n  },\n  'delta': {\n    carrier: 'Delta Dental',\n    requirements: {\n      general: [\n        'Include patient\\'s chief complaint',\n        'Clearly state the diagnosis',\n        'Explain the medical necessity',\n        'Justify the treatment provided',\n        'Reference specific CDT codes when applicable',\n        'Keep narrative between 500-1200 characters'\n      ],\n      procedureSpecific: {\n        'periodontal': [\n          'Include pocket depth measurements',\n          'Reference bone loss observed in radiographs',\n          'Describe mobility if present'\n        ],\n        'crown': [\n          'Specify extent of tooth destruction',\n          'Document any existing restorations',\n          'Explain why a less expensive restoration is not sufficient'\n        ],\n        'root canal': [\n          'Document pulpal involvement',\n          'Include symptoms of infection or pain',\n          'Reference radiographic findings'\n        ]\n      },\n      characterLimits: { min: 500, max: 1200 },\n      requiredAttachments: [\n        'Pre-operative X-rays for all crown and root canal procedures',\n        'Periodontal charting for all periodontal procedures'\n      ]\n    }\n  },\n  'cigna': {\n    carrier: 'Cigna Dental',\n    requirements: {\n      general: [\n        'Include patient\\'s chief complaint and symptoms',\n        'Provide clear diagnosis with supporting findings',\n        'Document medical necessity in detail',\n        'Explain why the specific treatment was chosen',\n        'Use appropriate dental terminology',\n        'Keep narrative between 600-1500 characters'\n      ],\n      procedureSpecific: {\n        'implant': [\n          'Document bone quality and quantity',\n          'Explain why other treatment options were not suitable',\n          'Include relevant medical history affecting implant suitability'\n        ],\n        'orthodontic': [\n          'Document functional impairments',\n          'Include details on occlusal relationship',\n          'Specify treatment duration expectations'\n        ]\n      },\n      characterLimits: { min: 600, max: 1500 },\n      requiredAttachments: [\n        'Full mouth series for implant cases',\n        'Cephalometric analysis for orthodontic cases',\n        'Intraoral photos when available'\n      ]\n    }\n  }\n};\n\n// Initialize PgVector for remote Supabase\nconst pgVector = new PgVector(process.env.POSTGRES_CONNECTION_STRING || 'postgresql://postgres:postgres@localhost:5432/postgres');\n\n// Log the database connection string (with password masked)\nconst connectionString = process.env.POSTGRES_CONNECTION_STRING || '';\nconst maskedConnectionString = connectionString.replace(/:[^:@]+@/, ':****@');\nconsole.log(`Initializing PgVector with connection: ${maskedConnectionString}`);\n\n// Create the vector query tool for insurance guidelines\nexport const insuranceGuidelinesQueryTool = createVectorQueryTool({\n  vectorStoreName: 'pgVector',\n  indexName: 'insurance_guidelines',\n  model: openai.embedding('text-embedding-3-small'),\n  reranker: {\n    model: openai('gpt-4o-mini'),\n    options: {\n      weights: {\n        semantic: 0.6,\n        vector: 0.3,\n        position: 0.1\n      },\n      topK: 5\n    }\n  },\n  description: 'Search insurance carrier guidelines for specific requirements related to dental procedures and claims'\n});\n\n// Tool to initialize and populate the remote Supabase database with insurance guidelines\nexport const initializeGuidelines = createTool({\n  id: 'initialize-guidelines',\n  description: 'Initialize and populate the remote Supabase database with insurance guidelines and vector embeddings',\n  outputSchema: z.object({\n    success: z.boolean(),\n    message: z.string()\n  }),\n  execute: async () => {\n    try {\n      console.log('Starting guidelines initialization process for remote Supabase...');\n      \n      // First, populate insurance carriers in the new schema\n      const carrierInserts = [];\n      const carrierIdMap = new Map<string, number>();\n      \n      for (const [carrierId, carrierData] of Object.entries(insuranceGuidelinesDB)) {\n        const carrierCode = carrierId.toUpperCase();\n        carrierInserts.push({\n          name: carrierData.carrier,\n          code: carrierCode,\n          created_at: new Date(),\n          updated_at: new Date()\n        });\n      }\n      \n      // Insert carriers into remote database using PgVector\n      const carrierResults = await pgVector.client.query(\n        'INSERT INTO insurance_carriers (carrier_name, carrier_type, created_at, updated_at) VALUES ($1, $2, NOW(), NOW()) RETURNING id, carrier_name',\n        ['Default/General', 'National']\n      );\n      carrierIdMap.set('default', carrierResults.rows[0].id);\n      \n      const deltaResult = await pgVector.client.query(\n        'INSERT INTO insurance_carriers (carrier_name, carrier_type, created_at, updated_at) VALUES ($1, $2, NOW(), NOW()) RETURNING id, carrier_name',\n        ['Delta Dental', 'National']\n      );\n      carrierIdMap.set('delta', deltaResult.rows[0].id);\n      \n      const cignaResult = await pgVector.client.query(\n        'INSERT INTO insurance_carriers (carrier_name, carrier_type, created_at, updated_at) VALUES ($1, $2, NOW(), NOW()) RETURNING id, carrier_name',\n        ['Cigna Dental', 'National']\n      );\n      carrierIdMap.set('cigna', cignaResult.rows[0].id);\n      \n      console.log('✅ Insurance carriers populated');\n      \n      // Create guideline documents for vector storage\n      const guidelineDocuments: string[] = [];\n      \n      // Process each insurance carrier's guidelines\n      for (const [carrierId, carrierData] of Object.entries(insuranceGuidelinesDB)) {\n        const dbCarrierId = carrierIdMap.get(carrierId);\n        if (!dbCarrierId) continue;\n        \n        // General guidelines\n        guidelineDocuments.push(`Insurance Carrier: ${carrierData.carrier}\\nGeneral Guidelines:\\n${carrierData.requirements.general.join('\\n')}`);\n        \n        // Character limits\n        if (carrierData.requirements.characterLimits) {\n          guidelineDocuments.push(\n            `Insurance Carrier: ${carrierData.carrier}\\nCharacter Limits:\\nMinimum: ${carrierData.requirements.characterLimits.min}\\nMaximum: ${carrierData.requirements.characterLimits.max}`\n          );\n        }\n        \n        // Required attachments\n        if (carrierData.requirements.requiredAttachments) {\n          guidelineDocuments.push(\n            `Insurance Carrier: ${carrierData.carrier}\\nRequired Attachments:\\n${carrierData.requirements.requiredAttachments.join('\\n')}`\n          );\n        }\n        \n        // Procedure-specific guidelines\n        if (carrierData.requirements.procedureSpecific) {\n          for (const [procedure, guidelines] of Object.entries(carrierData.requirements.procedureSpecific)) {\n            guidelineDocuments.push(\n              `Insurance Carrier: ${carrierData.carrier}\\nProcedure: ${procedure}\\nGuidelines:\\n${guidelines.join('\\n')}`\n            );\n          }\n        }\n      }\n      \n      // Create document objects\n      const documents = guidelineDocuments.map(text => MDocument.fromText(text));\n      \n      // Chunk documents\n      const allChunks = [];\n      for (const doc of documents) {\n        const chunks = await doc.chunk({\n          strategy: 'recursive',\n          size: 512,\n          overlap: 50,\n          separator: '\\n',\n        });\n        allChunks.push(...chunks);\n      }\n      \n      // Generate embeddings\n      const { embeddings } = await embedMany({\n        model: openai.embedding('text-embedding-3-small'),\n        values: allChunks.map(chunk => chunk.text),\n      });\n      \n      // Create index if it doesn't exist (for new embeddings table)\n      console.log('Attempting to create vector index in remote Supabase database...');\n      await pgVector.createIndex({\n        indexName: 'insurance_guidelines',\n        dimension: 1536, // Dimension for text-embedding-3-small\n      }).catch((error) => {\n        // Index may already exist, that's OK\n        console.log('Index creation result:', error?.message || 'Index may already exist');\n      });\n      \n      // Store embeddings in the new embeddings table\n      console.log(`Storing ${embeddings.length} vectors in remote Supabase database...`);\n      for (let i = 0; i < embeddings.length; i++) {\n        const embedding = embeddings[i];\n        const chunk = allChunks[i];\n        \n        await pgVector.client.query(\n          'INSERT INTO embeddings (content_type, content_id, embedding, metadata, created_at, updated_at) VALUES ($1, $2, $3, $4, NOW(), NOW())',\n          [\n            'guideline',\n            1, // Use 1 for guideline content since we don't have specific IDs\n            `[${embedding.join(',')}]`,\n            JSON.stringify({\n              text: chunk.text,\n              source: 'insurance_guidelines'\n            })\n          ]\n        );\n      }\n      \n      console.log('Successfully stored vectors in remote Supabase database');\n      \n      return {\n        success: true,\n        message: `Successfully initialized remote Supabase with ${carrierIdMap.size} carriers and ${allChunks.length} guideline chunks`\n      };\n    } catch (error) {\n      return {\n        success: false,\n        message: `Failed to initialize guidelines: ${error instanceof Error ? error.message : String(error)}`\n      };\n    }\n  }\n});\n\n// Enhanced dental narrator tool with improved processing\nexport const dentalNarratorTool = createTool({\n  id: 'generate-dental-narrative',\n  description: 'Generate insurance claim narratives from dental chart notes',\n  inputSchema: z.object({\n    chartNotes: z.string().describe('The dental chart notes to process'),\n    insuranceCarrier: z.string().optional().describe('The insurance carrier name, e.g., Delta Dental, Cigna'),\n    procedureCategory: z.string().optional().describe('The category of dental procedure, e.g., periodontal, crown, implant')\n  }),\n  outputSchema: z.object({\n    narrative: z.string().describe('The generated insurance claim narrative'),\n    characterCount: z.number().describe('Character count of the generated narrative'),\n    requiredAttachments: z.array(z.string()).optional().describe('List of required attachments for the claim'),\n    guidelines: z.array(z.string()).describe('The guidelines that were followed in creating the narrative'),\n    withinLimits: z.boolean().describe('Whether the narrative is within character limits'),\n    medicalHistory: z.string().optional().describe('Relevant medical history extracted from chart notes')\n  }),\n  execute: async ({ context }) => {\n    const insuranceCarrier = context.insuranceCarrier || 'default';\n    const procedureCategory = context.procedureCategory || '';\n    const chartNotes = context.chartNotes;\n    \n    try {\n      // Step 1: Retrieve insurance guidelines using RAG\n      // Create a query combining carrier and procedure\n      const guidelineQuery = `Insurance guidelines for ${insuranceCarrier} ${procedureCategory}`;\n      \n      // Generate embedding for the query\n      const { embedding: queryEmbedding } = await embed({\n        value: guidelineQuery,\n        model: openai.embedding('text-embedding-3-small'),\n      });\n      \n      // Query the vector database using the new embeddings table\n      console.log(`Querying remote Supabase database for guidelines related to: \"${guidelineQuery}\"`);\n      \n      // For now, do text-based search on metadata until embeddings are properly generated\n      const textSearchQuery = `\n        SELECT metadata->>'text' as text, \n               metadata->>'carrier' as carrier,\n               metadata->>'category' as category\n        FROM embeddings \n        WHERE content_type = 'guideline'\n        AND (\n          metadata->>'text' ILIKE $1 \n          OR metadata->>'carrier' ILIKE $2\n          OR metadata->>'category' ILIKE $3\n        )\n        LIMIT 5\n      `;\n      \n      const searchTerm = `%${insuranceCarrier}%`;\n      const categoryTerm = `%${procedureCategory}%`;\n      \n      const queryResults = await pgVector.client.query(textSearchQuery, [searchTerm, searchTerm, categoryTerm]);\n      console.log(`Retrieved ${queryResults.rows.length} results from remote Supabase database`);\n      \n      // Extract guidelines from results\n      const relevantGuidelines = queryResults.rows.map(row => row.text).filter(Boolean);\n      \n      // Combine guidelines into a single text\n      const guidelinesText = relevantGuidelines.join('\\n\\n');\n      \n      // Step 2: Process chart notes to extract key information\n      // Use OpenAI to extract information from chart notes\n      const extractionPrompt = `\nFrom the following dental chart notes, extract:\n1. Patient's chief complaints\n2. Diagnosis that aligns with the complaints\n3. Evidence of medical necessity\n4. Treatment justification\n5. Relevant medical history (if any)\n\nDental Chart Notes:\n${chartNotes}\n\nFormat your response as JSON with the following fields:\n{\n  \"complaints\": \"summary of chief complaints\",\n  \"diagnosis\": \"diagnosis that aligns with complaints\",\n  \"medicalNecessity\": \"detailed explanation of medical necessity\",\n  \"treatmentJustification\": \"explicit justification for the treatment\",\n  \"medicalHistory\": \"relevant medical history from the notes or 'None' if not present\"\n}\n`;\n\n      const extractionResponse = await generateText({\n        model: openai('gpt-4o-mini'),\n        prompt: extractionPrompt\n      });\n      \n      // Parse the extracted information\n      const extractedText = extractionResponse.text;\n      let extractedInfo;\n      \n      try {\n        // Extract JSON from the response\n        const jsonMatch = extractedText.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n          extractedInfo = JSON.parse(jsonMatch[0]);\n        } else {\n          throw new Error(\"Could not extract JSON from response\");\n        }\n      } catch (error) {\n        // Fallback if JSON parsing fails\n        extractedInfo = {\n          complaints: \"Patient's chief complaints could not be extracted\",\n          diagnosis: \"Diagnosis could not be extracted\",\n          medicalNecessity: \"Medical necessity could not be determined\",\n          treatmentJustification: \"Treatment justification could not be extracted\",\n          medicalHistory: \"None\"\n        };\n      }\n      \n      // Step 3: Generate narrative based on extracted info and guidelines\n      const narrativePrompt = `\nYou are generating an insurance claim narrative for a dental procedure.\n\nINSURANCE GUIDELINES:\n${guidelinesText}\n\nEXTRACTED PATIENT INFORMATION:\n- Chief Complaints: ${extractedInfo.complaints}\n- Diagnosis: ${extractedInfo.diagnosis}\n- Medical Necessity: ${extractedInfo.medicalNecessity}\n- Treatment Justification: ${extractedInfo.treatmentJustification}\n- Medical History: ${extractedInfo.medicalHistory}\n\nINSTRUCTIONS:\n1. Create a concise, professional narrative that explicitly addresses all requirements.\n2. ENSURE the narrative explicitly states treatment justification.\n3. Reference specific insurance guidelines where applicable.\n4. Include relevant medical history if significant.\n5. For multiple procedures, address each one clearly.\n6. Keep the narrative between ${getCharacterLimits(insuranceCarrier).min} and ${getCharacterLimits(insuranceCarrier).max} characters.\n7. Use clear, precise language aligned with insurance standards.\n\nGenerate the narrative now:\n`;\n\n      const narrativeResponse = await generateText({\n        model: openai('gpt-4o-mini'),\n        prompt: narrativePrompt\n      });\n      const generatedNarrative = narrativeResponse.text.trim();\n      \n      // Check character limits\n      const characterCount = generatedNarrative.length;\n      const limits = getCharacterLimits(insuranceCarrier);\n      const withinLimits = characterCount >= limits.min && characterCount <= limits.max;\n      \n      // If not within limits, adjust the narrative\n      let finalNarrative = generatedNarrative;\n      if (!withinLimits) {\n        if (characterCount < limits.min) {\n          // Narrative is too short, expand it\n          const expandPrompt = `\nThe following insurance narrative is too short (${characterCount} characters). \nPlease expand it to meet the minimum requirement of ${limits.min} characters\nwhile maintaining the same core information and professional tone.\nDo not add fictional information, but elaborate on existing points.\n\nNarrative to expand:\n${generatedNarrative}\n`;\n          const expandResponse = await generateText({\n            model: openai('gpt-4o-mini'),\n            prompt: expandPrompt\n          });\n          finalNarrative = expandResponse.text.trim();\n        } else if (characterCount > limits.max) {\n          // Narrative is too long, shorten it\n          const shortenPrompt = `\nThe following insurance narrative is too long (${characterCount} characters).\nPlease shorten it to meet the maximum requirement of ${limits.max} characters\nwhile preserving all essential information, especially treatment justification.\n\nNarrative to shorten:\n${generatedNarrative}\n`;\n          const shortenResponse = await generateText({\n            model: openai('gpt-4o-mini'),\n            prompt: shortenPrompt\n          });\n          finalNarrative = shortenResponse.text.trim();\n        }\n      }\n      \n      // Get required attachments\n      const requiredAttachments = getRequiredAttachments(insuranceCarrier, procedureCategory);\n      \n      // Return the final result\n      return {\n        narrative: finalNarrative,\n        characterCount: finalNarrative.length,\n        requiredAttachments,\n        guidelines: relevantGuidelines,\n        withinLimits: finalNarrative.length >= limits.min && finalNarrative.length <= limits.max,\n        medicalHistory: extractedInfo.medicalHistory !== 'None' ? extractedInfo.medicalHistory : undefined\n      };\n    } catch (error) {\n      // Handle errors\n      console.error('Error generating narrative:', error);\n      \n      // Return fallback response\n      return {\n        narrative: \"Error generating narrative. Please try again with more detailed chart notes.\",\n        characterCount: 0,\n        requiredAttachments: [],\n        guidelines: [],\n        withinLimits: false\n      };\n    }\n  }\n});\n\n// Helper function to get character limits for a carrier\nfunction getCharacterLimits(carrier: string): { min: number, max: number } {\n  const normalizedCarrier = carrier.toLowerCase();\n  let limits = { min: 500, max: 1500 }; // Default limits\n  \n  // Check for specific carrier limits\n  for (const [id, data] of Object.entries(insuranceGuidelinesDB)) {\n    if (normalizedCarrier.includes(id) && data.requirements.characterLimits) {\n      limits = data.requirements.characterLimits;\n      break;\n    }\n  }\n  \n  return limits;\n}\n\n// Helper function to get required attachments\nfunction getRequiredAttachments(carrier: string, procedure: string): string[] {\n  const normalizedCarrier = carrier.toLowerCase();\n  const normalizedProcedure = procedure.toLowerCase();\n  let attachments: string[] = [];\n  \n  // Find matching carrier\n  for (const [id, data] of Object.entries(insuranceGuidelinesDB)) {\n    if (normalizedCarrier.includes(id) && data.requirements.requiredAttachments) {\n      // Add all carrier attachments\n      attachments = [...data.requirements.requiredAttachments];\n      \n      // Filter for procedure-specific attachments if applicable\n      if (normalizedProcedure && attachments.length > 0) {\n        attachments = attachments.filter(attachment => \n          attachment.toLowerCase().includes(normalizedProcedure) ||\n          !attachment.toLowerCase().includes('for all')\n        );\n      }\n      \n      break;\n    }\n  }\n  \n  return attachments;\n}\n\n// Empty export to maintain compatibility\nexport const weatherTool = {} as any;\n"], "names": [], "mappings": ";;;;;;;AASA,MAAM,MAAA,GAAS,OAAA,CAAQ,GAAA,CAAI,cAAA,IAAkB,EAAA;AAC7C,MAAM,SAAA,GAAY,MAAA,CAAO,SAAA,CAAU,CAAA,EAAG,EAAE,CAAA,GAAI,KAAA,GAAQ,MAAA,CAAO,SAAA,CAAU,MAAA,CAAO,MAAA,GAAS,CAAC,CAAA;AACtF,OAAA,CAAQ,GAAA,CAAI,CAAA,yBAAA,EAA4B,SAAS,CAAA,CAAE,CAAA;AACnD,OAAA,CAAQ,GAAA,CAAI,CAAA,gBAAA,EAAmB,MAAA,CAAO,MAAM,CAAA,WAAA,CAAa,CAAA;AAczD,MAAM,qBAAA,GAA6D;AAAA,EACjE,SAAA,EAAW;AAAA,IACT,OAAA,EAAS,iBAAA;AAAA,IACT,YAAA,EAAc;AAAA,MACZ,OAAA,EAAS;AAAA,QACP,mCAAA;AAAA,QACA,6BAAA;AAAA,QACA,+BAAA;AAAA,QACA,gCAAA;AAAA,QACA;AAAA,OACF;AAAA,MACA,eAAA,EAAiB,EAAE,GAAA,EAAK,GAAA,EAAK,KAAK,IAAA;AAAK;AACzC,GACF;AAAA,EACA,OAAA,EAAS;AAAA,IACP,OAAA,EAAS,cAAA;AAAA,IACT,YAAA,EAAc;AAAA,MACZ,OAAA,EAAS;AAAA,QACP,mCAAA;AAAA,QACA,6BAAA;AAAA,QACA,+BAAA;AAAA,QACA,gCAAA;AAAA,QACA,8CAAA;AAAA,QACA;AAAA,OACF;AAAA,MACA,iBAAA,EAAmB;AAAA,QACjB,aAAA,EAAe;AAAA,UACb,mCAAA;AAAA,UACA,6CAAA;AAAA,UACA;AAAA,SACF;AAAA,QACA,OAAA,EAAS;AAAA,UACP,qCAAA;AAAA,UACA,oCAAA;AAAA,UACA;AAAA,SACF;AAAA,QACA,YAAA,EAAc;AAAA,UACZ,6BAAA;AAAA,UACA,uCAAA;AAAA,UACA;AAAA;AACF,OACF;AAAA,MACA,eAAA,EAAiB,EAAE,GAAA,EAAK,GAAA,EAAK,KAAK,IAAA,EAAK;AAAA,MACvC,mBAAA,EAAqB;AAAA,QACnB,8DAAA;AAAA,QACA;AAAA;AACF;AACF,GACF;AAAA,EACA,OAAA,EAAS;AAAA,IACP,OAAA,EAAS,cAAA;AAAA,IACT,YAAA,EAAc;AAAA,MACZ,OAAA,EAAS;AAAA,QACP,gDAAA;AAAA,QACA,kDAAA;AAAA,QACA,sCAAA;AAAA,QACA,+CAAA;AAAA,QACA,oCAAA;AAAA,QACA;AAAA,OACF;AAAA,MACA,iBAAA,EAAmB;AAAA,QACjB,SAAA,EAAW;AAAA,UACT,oCAAA;AAAA,UACA,uDAAA;AAAA,UACA;AAAA,SACF;AAAA,QACA,aAAA,EAAe;AAAA,UACb,iCAAA;AAAA,UACA,0CAAA;AAAA,UACA;AAAA;AACF,OACF;AAAA,MACA,eAAA,EAAiB,EAAE,GAAA,EAAK,GAAA,EAAK,KAAK,IAAA,EAAK;AAAA,MACvC,mBAAA,EAAqB;AAAA,QACnB,qCAAA;AAAA,QACA,8CAAA;AAAA,QACA;AAAA;AACF;AACF;AAEJ,CAAA;AAGA,MAAM,WAAW,IAAI,QAAA,CAAS,OAAA,CAAQ,GAAA,CAAI,8BAA8B,wDAAwD,CAAA;AAGhI,MAAM,gBAAA,GAAmB,OAAA,CAAQ,GAAA,CAAI,0BAAA,IAA8B,EAAA;AACnE,MAAM,sBAAA,GAAyB,gBAAA,CAAiB,OAAA,CAAQ,UAAA,EAAY,QAAQ,CAAA;AAC5E,OAAA,CAAQ,GAAA,CAAI,CAAA,uCAAA,EAA0C,sBAAsB,CAAA,CAAE,CAAA;AAGvE,MAAM,+BAA+B,qBAAA,CAAsB;AAAA,EAChE,eAAA,EAAiB,UAAA;AAAA,EACjB,SAAA,EAAW,sBAAA;AAAA,EACX,KAAA,EAAO,MAAA,CAAO,SAAA,CAAU,wBAAwB,CAAA;AAAA,EAChD,QAAA,EAAU;AAAA,IACR,KAAA,EAAO,OAAO,aAAa,CAAA;AAAA,IAC3B,OAAA,EAAS;AAAA,MACP,OAAA,EAAS;AAAA,QACP,QAAA,EAAU,GAAA;AAAA,QACV,MAAA,EAAQ,GAAA;AAAA,QACR,QAAA,EAAU;AAAA,OACZ;AAAA,MACA,IAAA,EAAM;AAAA;AACR,GACF;AAAA,EACA,WAAA,EAAa;AACf,CAAC;AAGM,MAAM,uBAAuB,UAAA,CAAW;AAAA,EAC7C,EAAA,EAAI,uBAAA;AAAA,EACJ,WAAA,EAAa,sGAAA;AAAA,EACb,YAAA,EAAc,EAAE,MAAA,CAAO;AAAA,IACrB,OAAA,EAAS,EAAE,OAAA,EAAQ;AAAA,IACnB,OAAA,EAAS,EAAE,MAAA;AAAO,GACnB,CAAA;AAAA,EACD,SAAS,YAAY;AACnB,IAAA,IAAI;AACF,MAAA,OAAA,CAAQ,IAAI,mEAAmE,CAAA;AAI/E,MAAA,MAAM,YAAA,uBAAmB,GAAA,EAAoB;AAE7C,MAAA,KAAA,MAAW,CAAC,SAAA,EAAW,WAAW,KAAK,MAAA,CAAO,OAAA,CAAQ,qBAAqB,CAAA,EAAG;AAC5E,QAAoB,UAAU,WAAA;AAM7B;AAIH,MAAA,MAAM,cAAA,GAAiB,MAAM,QAAA,CAAS,MAAA,CAAO,KAAA;AAAA,QAC3C,8IAAA;AAAA,QACA,CAAC,mBAAmB,UAAU;AAAA,OAChC;AACA,MAAA,YAAA,CAAa,IAAI,SAAA,EAAW,cAAA,CAAe,IAAA,CAAK,CAAC,EAAE,EAAE,CAAA;AAErD,MAAA,MAAM,WAAA,GAAc,MAAM,QAAA,CAAS,MAAA,CAAO,KAAA;AAAA,QACxC,8IAAA;AAAA,QACA,CAAC,gBAAgB,UAAU;AAAA,OAC7B;AACA,MAAA,YAAA,CAAa,IAAI,OAAA,EAAS,WAAA,CAAY,IAAA,CAAK,CAAC,EAAE,EAAE,CAAA;AAEhD,MAAA,MAAM,WAAA,GAAc,MAAM,QAAA,CAAS,MAAA,CAAO,KAAA;AAAA,QACxC,8IAAA;AAAA,QACA,CAAC,gBAAgB,UAAU;AAAA,OAC7B;AACA,MAAA,YAAA,CAAa,IAAI,OAAA,EAAS,WAAA,CAAY,IAAA,CAAK,CAAC,EAAE,EAAE,CAAA;AAEhD,MAAA,OAAA,CAAQ,IAAI,qCAAgC,CAAA;AAG5C,MAAA,MAAM,qBAA+B,EAAC;AAGtC,MAAA,KAAA,MAAW,CAAC,SAAA,EAAW,WAAW,KAAK,MAAA,CAAO,OAAA,CAAQ,qBAAqB,CAAA,EAAG;AAC5E,QAAA,MAAM,WAAA,GAAc,YAAA,CAAa,GAAA,CAAI,SAAS,CAAA;AAC9C,QAAA,IAAI,CAAC,WAAA,EAAa;AAGlB,QAAA,kBAAA,CAAmB,IAAA,CAAK,CAAA,mBAAA,EAAsB,WAAA,CAAY,OAAO;AAAA;AAAA,EAA0B,YAAY,YAAA,CAAa,OAAA,CAAQ,IAAA,CAAK,IAAI,CAAC,CAAA,CAAE,CAAA;AAGxI,QAAA,IAAI,WAAA,CAAY,aAAa,eAAA,EAAiB;AAC5C,UAAA,kBAAA,CAAmB,IAAA;AAAA,YACjB,CAAA,mBAAA,EAAsB,YAAY,OAAO;AAAA;AAAA,SAAA,EAAiC,WAAA,CAAY,YAAA,CAAa,eAAA,CAAgB,GAAG;AAAA,SAAA,EAAc,WAAA,CAAY,YAAA,CAAa,eAAA,CAAgB,GAAG,CAAA;AAAA,WAClL;AAAA;AAIF,QAAA,IAAI,WAAA,CAAY,aAAa,mBAAA,EAAqB;AAChD,UAAA,kBAAA,CAAmB,IAAA;AAAA,YACjB,CAAA,mBAAA,EAAsB,YAAY,OAAO;AAAA;AAAA,EAA4B,WAAA,CAAY,YAAA,CAAa,mBAAA,CAAoB,IAAA,CAAK,IAAI,CAAC,CAAA;AAAA,WAC9H;AAAA;AAIF,QAAA,IAAI,WAAA,CAAY,aAAa,iBAAA,EAAmB;AAC9C,UAAA,KAAA,MAAW,CAAC,WAAW,UAAU,CAAA,IAAK,OAAO,OAAA,CAAQ,WAAA,CAAY,YAAA,CAAa,iBAAiB,CAAA,EAAG;AAChG,YAAA,kBAAA,CAAmB,IAAA;AAAA,cACjB,CAAA,mBAAA,EAAsB,YAAY,OAAO;AAAA,WAAA,EAAgB,SAAS;AAAA;AAAA,EAAkB,UAAA,CAAW,IAAA,CAAK,IAAI,CAAC,CAAA;AAAA,aAC3G;AAAA;AACF;AACF;AAIF,MAAA,MAAM,YAAY,kBAAA,CAAmB,GAAA,CAAI,UAAQ,SAAA,CAAU,QAAA,CAAS,IAAI,CAAC,CAAA;AAGzE,MAAA,MAAM,YAAY,EAAC;AACnB,MAAA,KAAA,MAAW,OAAO,SAAA,EAAW;AAC3B,QAAA,MAAM,MAAA,GAAS,MAAM,GAAA,CAAI,KAAA,CAAM;AAAA,UAC7B,QAAA,EAAU,WAAA;AAAA,UACV,IAAA,EAAM,GAAA;AAAA,UACN,OAAA,EAAS,EAAA;AAAA,UACT,SAAA,EAAW;AAAA,SACZ,CAAA;AACD,QAAA,SAAA,CAAU,IAAA,CAAK,GAAG,MAAM,CAAA;AAAA;AAI1B,MAAA,MAAM,EAAE,UAAA,EAAW,GAAI,MAAM,SAAA,CAAU;AAAA,QACrC,KAAA,EAAO,MAAA,CAAO,SAAA,CAAU,wBAAwB,CAAA;AAAA,QAChD,MAAA,EAAQ,SAAA,CAAU,GAAA,CAAI,CAAA,KAAA,KAAS,MAAM,IAAI;AAAA,OAC1C,CAAA;AAGD,MAAA,OAAA,CAAQ,IAAI,kEAAkE,CAAA;AAC9E,MAAA,MAAM,SAAS,WAAA,CAAY;AAAA,QACzB,SAAA,EAAW,sBAAA;AAAA,QACX,SAAA,EAAW;AAAA;AAAA,OACZ,CAAA,CAAE,KAAA,CAAM,CAAC,KAAA,KAAU;AAElB,QAAA,OAAA,CAAQ,GAAA,CAAI,wBAAA,EAA0B,KAAA,EAAO,OAAA,IAAW,yBAAyB,CAAA;AAAA,OAClF,CAAA;AAGD,MAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,QAAA,EAAW,UAAA,CAAW,MAAM,CAAA,uCAAA,CAAyC,CAAA;AACjF,MAAA,KAAA,IAAS,CAAA,GAAI,CAAA,EAAG,CAAA,GAAI,UAAA,CAAW,QAAQ,CAAA,EAAA,EAAK;AAC1C,QAAA,MAAM,SAAA,GAAY,WAAW,CAAC,CAAA;AAC9B,QAAA,MAAM,KAAA,GAAQ,UAAU,CAAC,CAAA;AAEzB,QAAA,MAAM,SAAS,MAAA,CAAO,KAAA;AAAA,UACpB,sIAAA;AAAA,UACA;AAAA,YACE,WAAA;AAAA,YACA,CAAA;AAAA;AAAA,YACA,CAAA,CAAA,EAAI,SAAA,CAAU,IAAA,CAAK,GAAG,CAAC,CAAA,CAAA,CAAA;AAAA,YACvB,KAAK,SAAA,CAAU;AAAA,cACb,MAAM,KAAA,CAAM,IAAA;AAAA,cACZ,MAAA,EAAQ;AAAA,aACT;AAAA;AACH,SACF;AAAA;AAGF,MAAA,OAAA,CAAQ,IAAI,yDAAyD,CAAA;AAErE,MAAA,OAAO;AAAA,QACL,OAAA,EAAS,IAAA;AAAA,QACT,SAAS,CAAA,8CAAA,EAAiD,YAAA,CAAa,IAAI,CAAA,cAAA,EAAiB,UAAU,MAAM,CAAA,iBAAA;AAAA,OAC9G;AAAA,aACO,KAAA,EAAO;AACd,MAAA,OAAO;AAAA,QACL,OAAA,EAAS,KAAA;AAAA,QACT,OAAA,EAAS,oCAAoC,KAAA,YAAiB,KAAA,GAAQ,MAAM,OAAA,GAAU,MAAA,CAAO,KAAK,CAAC,CAAA;AAAA,OACrG;AAAA;AACF;AAEJ,CAAC;AAGM,MAAM,qBAAqB,UAAA,CAAW;AAAA,EAC3C,EAAA,EAAI,2BAAA;AAAA,EACJ,WAAA,EAAa,6DAAA;AAAA,EACb,WAAA,EAAa,EAAE,MAAA,CAAO;AAAA,IACpB,UAAA,EAAY,CAAA,CAAE,MAAA,EAAO,CAAE,SAAS,mCAAmC,CAAA;AAAA,IACnE,kBAAkB,CAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CAAE,SAAS,uDAAuD,CAAA;AAAA,IACxG,mBAAmB,CAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CAAE,SAAS,qEAAqE;AAAA,GACxH,CAAA;AAAA,EACD,YAAA,EAAc,EAAE,MAAA,CAAO;AAAA,IACrB,SAAA,EAAW,CAAA,CAAE,MAAA,EAAO,CAAE,SAAS,yCAAyC,CAAA;AAAA,IACxE,cAAA,EAAgB,CAAA,CAAE,MAAA,EAAO,CAAE,SAAS,4CAA4C,CAAA;AAAA,IAChF,mBAAA,EAAqB,CAAA,CAAE,KAAA,CAAM,CAAA,CAAE,MAAA,EAAQ,CAAA,CAAE,QAAA,EAAS,CAAE,QAAA,CAAS,4CAA4C,CAAA;AAAA,IACzG,UAAA,EAAY,EAAE,KAAA,CAAM,CAAA,CAAE,QAAQ,CAAA,CAAE,SAAS,6DAA6D,CAAA;AAAA,IACtG,YAAA,EAAc,CAAA,CAAE,OAAA,EAAQ,CAAE,SAAS,kDAAkD,CAAA;AAAA,IACrF,gBAAgB,CAAA,CAAE,MAAA,GAAS,QAAA,EAAS,CAAE,SAAS,qDAAqD;AAAA,GACrG,CAAA;AAAA,EACD,OAAA,EAAS,OAAO,EAAE,OAAA,EAAQ,KAAM;AAC9B,IAAA,MAAM,gBAAA,GAAmB,QAAQ,gBAAA,IAAoB,SAAA;AACrD,IAAA,MAAM,iBAAA,GAAoB,QAAQ,iBAAA,IAAqB,EAAA;AACvD,IAAA,MAAM,aAAa,OAAA,CAAQ,UAAA;AAE3B,IAAA,IAAI;AAGF,MAAA,MAAM,cAAA,GAAiB,CAAA,yBAAA,EAA4B,gBAAgB,CAAA,CAAA,EAAI,iBAAiB,CAAA,CAAA;AAGxF,MAAsC,MAAM,KAAA,CAAM;AAAA,QAChD,KAAA,EAAO,cAAA;AAAA,QACP,KAAA,EAAO,MAAA,CAAO,SAAA,CAAU,wBAAwB;AAAA,OACjD;AAGD,MAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,8DAAA,EAAiE,cAAc,CAAA,CAAA,CAAG,CAAA;AAG9F,MAAA,MAAM,eAAA,GAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAA,CAAA;AAcxB,MAAA,MAAM,UAAA,GAAa,IAAI,gBAAgB,CAAA,CAAA,CAAA;AACvC,MAAA,MAAM,YAAA,GAAe,IAAI,iBAAiB,CAAA,CAAA,CAAA;AAE1C,MAAA,MAAM,YAAA,GAAe,MAAM,QAAA,CAAS,MAAA,CAAO,KAAA,CAAM,iBAAiB,CAAC,UAAA,EAAY,UAAA,EAAY,YAAY,CAAC,CAAA;AACxG,MAAA,OAAA,CAAQ,GAAA,CAAI,CAAA,UAAA,EAAa,YAAA,CAAa,IAAA,CAAK,MAAM,CAAA,sCAAA,CAAwC,CAAA;AAGzF,MAAA,MAAM,kBAAA,GAAqB,aAAa,IAAA,CAAK,GAAA,CAAI,SAAO,GAAA,CAAI,IAAI,CAAA,CAAE,MAAA,CAAO,OAAO,CAAA;AAGhF,MAAA,MAAM,cAAA,GAAiB,kBAAA,CAAmB,IAAA,CAAK,MAAM,CAAA;AAIrD,MAAA,MAAM,gBAAA,GAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA,EAS7B,UAAU;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CAAA;AAYN,MAAA,MAAM,kBAAA,GAAqB,MAAM,YAAA,CAAa;AAAA,QAC5C,KAAA,EAAO,OAAO,aAAa,CAAA;AAAA,QAC3B,MAAA,EAAQ;AAAA,OACT,CAAA;AAGD,MAAA,MAAM,gBAAgB,kBAAA,CAAmB,IAAA;AACzC,MAAA,IAAI,aAAA;AAEJ,MAAA,IAAI;AAEF,QAAA,MAAM,SAAA,GAAY,aAAA,CAAc,KAAA,CAAM,aAAa,CAAA;AACnD,QAAA,IAAI,SAAA,EAAW;AACb,UAAA,aAAA,GAAgB,IAAA,CAAK,KAAA,CAAM,SAAA,CAAU,CAAC,CAAC,CAAA;AAAA,SACzC,MAAO;AACL,UAAA,MAAM,IAAI,MAAM,sCAAsC,CAAA;AAAA;AACxD,eACO,KAAA,EAAO;AAEd,QAAA,aAAA,GAAgB;AAAA,UACd,UAAA,EAAY,mDAAA;AAAA,UACZ,SAAA,EAAW,kCAAA;AAAA,UACX,gBAAA,EAAkB,2CAAA;AAAA,UAClB,sBAAA,EAAwB,gDAAA;AAAA,UACxB,cAAA,EAAgB;AAAA,SAClB;AAAA;AAIF,MAAA,MAAM,eAAA,GAAkB;AAAA;;AAAA;AAAA,EAI5B,cAAc;;AAAA;AAAA,oBAAA,EAGM,cAAc,UAAU;AAAA,aAAA,EAC/B,cAAc,SAAS;AAAA,qBAAA,EACf,cAAc,gBAAgB;AAAA,2BAAA,EACxB,cAAc,sBAAsB;AAAA,mBAAA,EAC5C,cAAc,cAAc;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BAAA,EAQjB,kBAAA,CAAmB,gBAAgB,CAAA,CAAE,GAAG,QAAQ,kBAAA,CAAmB,gBAAgB,EAAE,GAAG,CAAA;AAAA;;AAAA;AAAA,CAAA;AAMlH,MAAA,MAAM,iBAAA,GAAoB,MAAM,YAAA,CAAa;AAAA,QAC3C,KAAA,EAAO,OAAO,aAAa,CAAA;AAAA,QAC3B,MAAA,EAAQ;AAAA,OACT,CAAA;AACD,MAAA,MAAM,kBAAA,GAAqB,iBAAA,CAAkB,IAAA,CAAK,IAAA,EAAK;AAGvD,MAAA,MAAM,iBAAiB,kBAAA,CAAmB,MAAA;AAC1C,MAAA,MAAM,MAAA,GAAS,mBAAmB,gBAAgB,CAAA;AAClD,MAAA,MAAM,YAAA,GAAe,cAAA,IAAkB,MAAA,CAAO,GAAA,IAAO,kBAAkB,MAAA,CAAO,GAAA;AAG9E,MAAA,IAAI,cAAA,GAAiB,kBAAA;AACrB,MAAA,IAAI,CAAC,YAAA,EAAc;AACjB,QAAA,IAAI,cAAA,GAAiB,OAAO,GAAA,EAAK;AAE/B,UAAA,MAAM,YAAA,GAAe;AAAA,gDAAA,EACmB,cAAc,CAAA;AAAA,oDAAA,EACV,OAAO,GAAG,CAAA;AAAA;AAAA;;AAAA;AAAA,EAK9D,kBAAkB;AAAA,CAAA;AAEV,UAAA,MAAM,cAAA,GAAiB,MAAM,YAAA,CAAa;AAAA,YACxC,KAAA,EAAO,OAAO,aAAa,CAAA;AAAA,YAC3B,MAAA,EAAQ;AAAA,WACT,CAAA;AACD,UAAA,cAAA,GAAiB,cAAA,CAAe,KAAK,IAAA,EAAK;AAAA,SAC5C,MAAA,IAAW,cAAA,GAAiB,MAAA,CAAO,GAAA,EAAK;AAEtC,UAAA,MAAM,aAAA,GAAgB;AAAA,+CAAA,EACiB,cAAc,CAAA;AAAA,qDAAA,EACR,OAAO,GAAG,CAAA;AAAA;;AAAA;AAAA,EAI/D,kBAAkB;AAAA,CAAA;AAEV,UAAA,MAAM,eAAA,GAAkB,MAAM,YAAA,CAAa;AAAA,YACzC,KAAA,EAAO,OAAO,aAAa,CAAA;AAAA,YAC3B,MAAA,EAAQ;AAAA,WACT,CAAA;AACD,UAAA,cAAA,GAAiB,eAAA,CAAgB,KAAK,IAAA,EAAK;AAAA;AAC7C;AAIF,MAAA,MAAM,mBAAA,GAAsB,sBAAA,CAAuB,gBAAA,EAAkB,iBAAiB,CAAA;AAGtF,MAAA,OAAO;AAAA,QACL,SAAA,EAAW,cAAA;AAAA,QACX,gBAAgB,cAAA,CAAe,MAAA;AAAA,QAC/B,mBAAA;AAAA,QACA,UAAA,EAAY,kBAAA;AAAA,QACZ,cAAc,cAAA,CAAe,MAAA,IAAU,OAAO,GAAA,IAAO,cAAA,CAAe,UAAU,MAAA,CAAO,GAAA;AAAA,QACrF,cAAA,EAAgB,aAAA,CAAc,cAAA,KAAmB,MAAA,GAAS,cAAc,cAAA,GAAiB;AAAA,OAC3F;AAAA,aACO,KAAA,EAAO;AAEd,MAAA,OAAA,CAAQ,KAAA,CAAM,+BAA+B,KAAK,CAAA;AAGlD,MAAA,OAAO;AAAA,QACL,SAAA,EAAW,8EAAA;AAAA,QACX,cAAA,EAAgB,CAAA;AAAA,QAChB,qBAAqB,EAAC;AAAA,QACtB,YAAY,EAAC;AAAA,QACb,YAAA,EAAc;AAAA,OAChB;AAAA;AACF;AAEJ,CAAC;AAGD,SAAS,mBAAmB,OAAA,EAA+C;AACzE,EAAA,MAAM,iBAAA,GAAoB,QAAQ,WAAA,EAAY;AAC9C,EAAA,IAAI,MAAA,GAAS,EAAE,GAAA,EAAK,GAAA,EAAK,KAAK,IAAA,EAAK;AAGnC,EAAA,KAAA,MAAW,CAAC,EAAA,EAAI,IAAI,KAAK,MAAA,CAAO,OAAA,CAAQ,qBAAqB,CAAA,EAAG;AAC9D,IAAA,IAAI,kBAAkB,QAAA,CAAS,EAAE,CAAA,IAAK,IAAA,CAAK,aAAa,eAAA,EAAiB;AACvE,MAAA,MAAA,GAAS,KAAK,YAAA,CAAa,eAAA;AAC3B,MAAA;AAAA;AACF;AAGF,EAAA,OAAO,MAAA;AACT;AAGA,SAAS,sBAAA,CAAuB,SAAiB,SAAA,EAA6B;AAC5E,EAAA,MAAM,iBAAA,GAAoB,QAAQ,WAAA,EAAY;AAC9C,EAAA,MAAM,mBAAA,GAAsB,UAAU,WAAA,EAAY;AAClD,EAAA,IAAI,cAAwB,EAAC;AAG7B,EAAA,KAAA,MAAW,CAAC,EAAA,EAAI,IAAI,KAAK,MAAA,CAAO,OAAA,CAAQ,qBAAqB,CAAA,EAAG;AAC9D,IAAA,IAAI,kBAAkB,QAAA,CAAS,EAAE,CAAA,IAAK,IAAA,CAAK,aAAa,mBAAA,EAAqB;AAE3E,MAAA,WAAA,GAAc,CAAC,GAAG,IAAA,CAAK,YAAA,CAAa,mBAAmB,CAAA;AAGvD,MAAA,IAAI,mBAAA,IAAuB,WAAA,CAAY,MAAA,GAAS,CAAA,EAAG;AACjD,QAAA,WAAA,GAAc,WAAA,CAAY,MAAA;AAAA,UAAO,CAAA,UAAA,KAC/B,UAAA,CAAW,WAAA,EAAY,CAAE,QAAA,CAAS,mBAAmB,CAAA,IACrD,CAAC,UAAA,CAAW,WAAA,EAAY,CAAE,QAAA,CAAS,SAAS;AAAA,SAC9C;AAAA;AAGF,MAAA;AAAA;AACF;AAGF,EAAA,OAAO,WAAA;AACT;AAGO,MAAM,cAAc;;;;"}
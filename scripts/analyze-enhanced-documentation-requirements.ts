#!/usr/bin/env ts-node

import { Client } from 'pg';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

class DocumentationRequirementsAnalyzer {
  private client: Client;

  constructor() {
    this.client = new Client({
      connectionString: process.env.POSTGRES_CONNECTION_STRING || process.env.DATABASE_URL
    });
  }

  async analyzeRequirements(): Promise<void> {
    console.log('📊 Analyzing Enhanced Documentation Requirements');
    console.log('==============================================\n');

    await this.client.connect();

    try {
      // Total count
      const totalResult = await this.client.query('SELECT COUNT(*) as total FROM documentation_requirements');
      console.log(`📋 Total documentation requirements: ${totalResult.rows[0].total}`);

      // Count by category
      console.log('\n📂 Requirements by Category:');
      const categoryResult = await this.client.query(`
        SELECT category, COUNT(*) as count 
        FROM documentation_requirements 
        WHERE category IS NOT NULL
        GROUP BY category 
        ORDER BY count DESC
      `);

      categoryResult.rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ${row.category}: ${row.count} requirements`);
      });

      // Count by priority
      console.log('\n🎯 Requirements by Priority:');
      const priorityResult = await this.client.query(`
        SELECT priority, COUNT(*) as count 
        FROM documentation_requirements 
        WHERE priority IS NOT NULL
        GROUP BY priority 
        ORDER BY 
          CASE priority 
            WHEN 'high' THEN 1 
            WHEN 'medium' THEN 2 
            WHEN 'low' THEN 3 
          END
      `);

      priorityResult.rows.forEach((row, index) => {
        const emoji = row.priority === 'high' ? '🔴' : row.priority === 'medium' ? '🟡' : '🟢';
        console.log(`  ${emoji} ${row.priority}: ${row.count} requirements`);
      });

      // Most common document types
      console.log('\n📄 Most Common Document Types:');
      const typeResult = await this.client.query(`
        SELECT document_type, COUNT(*) as count 
        FROM documentation_requirements 
        GROUP BY document_type 
        ORDER BY count DESC 
        LIMIT 15
      `);

      typeResult.rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ${row.document_type}: ${row.count} occurrences`);
      });

      // Sample high priority requirements
      console.log('\n🔴 Sample High Priority Requirements:');
      const highPriorityResult = await this.client.query(`
        SELECT document_type, description, category
        FROM documentation_requirements 
        WHERE priority = 'high'
        ORDER BY document_type
        LIMIT 10
      `);

      highPriorityResult.rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ${row.document_type} (${row.category})`);
        console.log(`     ${row.description.substring(0, 80)}...`);
      });

      // Requirements by carrier
      console.log('\n🏥 Requirements by Carrier:');
      const carrierResult = await this.client.query(`
        SELECT 
          ic.carrier_name,
          COUNT(dr.id) as requirement_count
        FROM documentation_requirements dr
        LEFT JOIN insurance_carriers ic ON dr.carrier_id = ic.id
        GROUP BY ic.carrier_name
        ORDER BY requirement_count DESC
        LIMIT 10
      `);

      carrierResult.rows.forEach((row, index) => {
        const carrierName = row.carrier_name || 'General/Unspecified';
        console.log(`  ${index + 1}. ${carrierName}: ${row.requirement_count} requirements`);
      });

      // Advanced documentation types (new categories)
      console.log('\n🚀 Advanced Documentation Categories:');
      const advancedCategories = [
        'technology_integration',
        'remote_care', 
        'modern_technology',
        'advanced_procedures',
        'implant_procedures',
        'anesthesia_monitoring'
      ];

      for (const category of advancedCategories) {
        const result = await this.client.query(`
          SELECT COUNT(*) as count 
          FROM documentation_requirements 
          WHERE category = $1
        `, [category]);
        
        if (parseInt(result.rows[0].count) > 0) {
          console.log(`  🔬 ${category}: ${result.rows[0].count} requirements`);
        }
      }

      // Sample advanced requirements
      console.log('\n🔬 Sample Advanced Technology Requirements:');
      const advancedResult = await this.client.query(`
        SELECT document_type, description, category
        FROM documentation_requirements 
        WHERE category IN ('technology_integration', 'modern_technology', 'advanced_procedures')
        LIMIT 5
      `);

      advancedResult.rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ${row.document_type}`);
        console.log(`     Category: ${row.category}`);
        console.log(`     ${row.description.substring(0, 100)}...`);
      });

      // Format requirements analysis
      console.log('\n📋 Format Requirements Analysis:');
      const formatResult = await this.client.query(`
        SELECT 
          CASE 
            WHEN format_requirements ILIKE '%digital%' THEN 'Digital Format'
            WHEN format_requirements ILIKE '%physical%' THEN 'Physical Format'
            WHEN format_requirements ILIKE '%signed%' THEN 'Signed Documents'
            WHEN format_requirements ILIKE '%radiograph%' THEN 'Radiographic'
            WHEN format_requirements ILIKE '%assessment%' THEN 'Assessment Scales'
            ELSE 'Standard Format'
          END as format_type,
          COUNT(*) as count
        FROM documentation_requirements 
        WHERE format_requirements IS NOT NULL
        GROUP BY format_type
        ORDER BY count DESC
      `);

      formatResult.rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ${row.format_type}: ${row.count} requirements`);
      });

      // Coverage analysis
      console.log('\n📈 Documentation Coverage Analysis:');
      
      // Check coverage across different procedure categories
      const coverageResult = await this.client.query(`
        SELECT 
          CASE 
            WHEN document_type ILIKE '%x-ray%' OR document_type ILIKE '%radiograph%' THEN 'Radiographic'
            WHEN document_type ILIKE '%photo%' OR document_type ILIKE '%image%' THEN 'Photography'
            WHEN document_type ILIKE '%chart%' OR document_type ILIKE '%assessment%' THEN 'Clinical Assessment'
            WHEN document_type ILIKE '%consent%' OR document_type ILIKE '%authorization%' THEN 'Legal/Compliance'
            WHEN document_type ILIKE '%model%' OR document_type ILIKE '%impression%' THEN 'Diagnostic Models'
            WHEN document_type ILIKE '%surgical%' OR document_type ILIKE '%implant%' THEN 'Surgical Documentation'
            ELSE 'Other'
          END as doc_category,
          COUNT(*) as count
        FROM documentation_requirements
        GROUP BY doc_category
        ORDER BY count DESC
      `);

      coverageResult.rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ${row.doc_category}: ${row.count} requirements`);
      });

      console.log('\n🎉 Enhanced documentation requirements analysis completed!');
      console.log('\n📝 Summary:');
      console.log(`   • Total requirements: ${totalResult.rows[0].total}`);
      console.log(`   • Categories covered: ${categoryResult.rows.length}`);
      console.log(`   • High priority items: ${priorityResult.rows.find(r => r.priority === 'high')?.count || 0}`);
      console.log(`   • Carriers with specific requirements: ${carrierResult.rows.filter(r => r.carrier_name).length}`);

    } finally {
      await this.client.end();
    }
  }

  async run(): Promise<void> {
    try {
      await this.analyzeRequirements();
      
    } catch (error) {
      console.error('❌ Analysis failed:', error);
      throw error;
    }
  }
}

// Main execution
if (require.main === module) {
  const analyzer = new DocumentationRequirementsAnalyzer();
  analyzer.run().catch(console.error);
}

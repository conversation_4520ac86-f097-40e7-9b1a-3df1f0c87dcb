#!/usr/bin/env ts-node

/**
 * Test script for the new dental endpoints
 * Tests all 5 new endpoints with sample data
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000';

interface TestResult {
  endpoint: string;
  success: boolean;
  error?: string;
  responseTime?: number;
}

class EndpointTester {
  private results: TestResult[] = [];

  async testGenerateNarrative(): Promise<TestResult> {
    const startTime = Date.now();
    const endpoint = '/generate/narrative';
    
    try {
      const response = await axios.post(`${BASE_URL}${endpoint}`, {
        patient_info: {
          age: 45,
          member_id: "TEST123"
        },
        procedures: [
          {
            cdt_code: "D2150",
            description: "Amalgam restoration - two surfaces",
            tooth_number: "14"
          }
        ],
        carrier: "Aetna",
        claim_type: "initial",
        documentation_available: ["clinical_notes", "x-rays"],
        clinical_notes: "<PERSON><PERSON> presents with carious lesion on tooth #14 requiring restoration."
      });

      return {
        endpoint,
        success: response.status === 200 && response.data.success,
        responseTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        endpoint,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime: Date.now() - startTime
      };
    }
  }

  async testAnalyzeClaim(): Promise<TestResult> {
    const startTime = Date.now();
    const endpoint = '/analyze/claim';
    
    try {
      const response = await axios.post(`${BASE_URL}${endpoint}`, {
        procedures: [
          {
            cdt_code: "D2150",
            description: "Amalgam restoration",
            date_of_service: "2024-01-15",
            tooth_number: "14"
          }
        ],
        carrier: "Aetna",
        documentation_submitted: ["clinical_notes"],
        patient_age: 45,
        claim_amount: 250.00
      });

      return {
        endpoint,
        success: response.status === 200 && response.data.success,
        responseTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        endpoint,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime: Date.now() - startTime
      };
    }
  }

  async testDocumentationRequirements(): Promise<TestResult> {
    const startTime = Date.now();
    const endpoint = '/documentation/requirements';
    
    try {
      const response = await axios.post(`${BASE_URL}${endpoint}`, {
        cdt_codes: ["D2150", "D1110"],
        carrier: "Aetna",
        patient_age: 45,
        treatment_area: "posterior"
      });

      return {
        endpoint,
        success: response.status === 200 && response.data.success,
        responseTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        endpoint,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime: Date.now() - startTime
      };
    }
  }

  async testAppealAssistance(): Promise<TestResult> {
    const startTime = Date.now();
    const endpoint = '/appeal/assistance';
    
    try {
      const response = await axios.post(`${BASE_URL}${endpoint}`, {
        original_claim: {
          procedures: [
            {
              cdt_code: "D2150",
              description: "Amalgam restoration",
              denied_amount: 250.00
            }
          ],
          denial_reasons: ["Insufficient documentation", "Medical necessity not established"],
          carrier: "Aetna"
        },
        additional_documentation: ["radiographs"],
        clinical_justification: "Patient has extensive carious lesion requiring immediate restoration to prevent further decay."
      });

      return {
        endpoint,
        success: response.status === 200 && response.data.success,
        responseTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        endpoint,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime: Date.now() - startTime
      };
    }
  }

  async testPredeterminationAnalysis(): Promise<TestResult> {
    const startTime = Date.now();
    const endpoint = '/predetermination/analysis';
    
    try {
      const response = await axios.post(`${BASE_URL}${endpoint}`, {
        planned_procedures: [
          {
            cdt_code: "D2750",
            description: "Crown - porcelain fused to metal",
            estimated_fee: 1200.00,
            tooth_number: "14",
            priority: "necessary"
          }
        ],
        carrier: "Aetna",
        patient_info: {
          age: 45,
          member_id: "TEST123",
          plan_type: "PPO"
        },
        treatment_plan_notes: "Crown recommended due to extensive restoration and fracture risk."
      });

      return {
        endpoint,
        success: response.status === 200 && response.data.success,
        responseTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        endpoint,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime: Date.now() - startTime
      };
    }
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 Testing new dental endpoints...\n');

    // Test each endpoint
    this.results.push(await this.testGenerateNarrative());
    this.results.push(await this.testAnalyzeClaim());
    this.results.push(await this.testDocumentationRequirements());
    this.results.push(await this.testAppealAssistance());
    this.results.push(await this.testPredeterminationAnalysis());

    // Print results
    this.printResults();
  }

  private printResults(): void {
    console.log('📊 Test Results:');
    console.log('='.repeat(80));

    let successCount = 0;
    
    for (const result of this.results) {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      const time = result.responseTime ? `${result.responseTime}ms` : 'N/A';
      
      console.log(`${status} ${result.endpoint.padEnd(30)} (${time})`);
      
      if (!result.success && result.error) {
        console.log(`    Error: ${result.error}`);
      }
      
      if (result.success) successCount++;
    }

    console.log('='.repeat(80));
    console.log(`Summary: ${successCount}/${this.results.length} tests passed`);
    
    if (successCount === this.results.length) {
      console.log('🎉 All endpoints are working correctly!');
    } else {
      console.log('⚠️  Some endpoints need attention.');
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new EndpointTester();
  tester.runAllTests().catch(console.error);
}

export { EndpointTester };

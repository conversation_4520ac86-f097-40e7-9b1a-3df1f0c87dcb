#!/usr/bin/env ts-node

import { Client } from 'pg';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

interface FrequencyLimitation {
  procedure_code: string;
  frequency_limitation: string;
  description: string;
  category: string;
}

class FrequencyLimitationImplementer {
  private createClient(): Client {
    return new Client({
      connectionString: process.env.POSTGRES_CONNECTION_STRING || process.env.DATABASE_URL
    });
  }

  // Standard dental procedure frequency limitations based on industry standards
  private getStandardFrequencyLimitations(): FrequencyLimitation[] {
    return [
      // Preventive Care
      { procedure_code: 'D1110', frequency_limitation: 'Every 6 months', description: 'Adult prophylaxis', category: 'Preventive' },
      { procedure_code: 'D1120', frequency_limitation: 'Every 3 months', description: 'Child prophylaxis', category: 'Preventive' },
      { procedure_code: 'D1206', frequency_limitation: 'Every 6 months', description: 'Topical fluoride varnish', category: 'Preventive' },
      { procedure_code: 'D1208', frequency_limitation: 'Every 6 months', description: 'Topical fluoride', category: 'Preventive' },
      
      // Diagnostic X-rays
      { procedure_code: 'D0210', frequency_limitation: 'Every 3 years', description: 'Intraoral complete series', category: 'Diagnostic' },
      { procedure_code: 'D0220', frequency_limitation: 'Every 18 months', description: 'Intraoral periapical first film', category: 'Diagnostic' },
      { procedure_code: 'D0230', frequency_limitation: 'Every 18 months', description: 'Intraoral periapical each additional', category: 'Diagnostic' },
      { procedure_code: 'D0270', frequency_limitation: 'Every 12 months', description: 'Bitewing single film', category: 'Diagnostic' },
      { procedure_code: 'D0272', frequency_limitation: 'Every 12 months', description: 'Bitewings two films', category: 'Diagnostic' },
      { procedure_code: 'D0274', frequency_limitation: 'Every 12 months', description: 'Bitewings four films', category: 'Diagnostic' },
      { procedure_code: 'D0330', frequency_limitation: 'Every 3 years', description: 'Panoramic film', category: 'Diagnostic' },
      
      // Restorative - Crowns and Major Work
      { procedure_code: 'D2740', frequency_limitation: 'Every 5 years', description: 'Crown - porcelain/ceramic substrate', category: 'Restorative' },
      { procedure_code: 'D2750', frequency_limitation: 'Every 5 years', description: 'Crown - porcelain fused to metal', category: 'Restorative' },
      { procedure_code: 'D2790', frequency_limitation: 'Every 5 years', description: 'Crown - full cast metal', category: 'Restorative' },
      { procedure_code: 'D2791', frequency_limitation: 'Every 5 years', description: 'Crown - full cast metal', category: 'Restorative' },
      { procedure_code: 'D2792', frequency_limitation: 'Every 5 years', description: 'Crown - full cast metal', category: 'Restorative' },
      
      // Endodontics
      { procedure_code: 'D3310', frequency_limitation: 'Once per lifetime', description: 'Endodontic therapy, anterior tooth', category: 'Endodontics' },
      { procedure_code: 'D3320', frequency_limitation: 'Once per lifetime', description: 'Endodontic therapy, premolar', category: 'Endodontics' },
      { procedure_code: 'D3330', frequency_limitation: 'Once per lifetime', description: 'Endodontic therapy, molar', category: 'Endodontics' },
      
      // Periodontics
      { procedure_code: 'D4341', frequency_limitation: 'Every 24 months', description: 'Periodontal scaling and root planing - four or more teeth per quadrant', category: 'Periodontics' },
      { procedure_code: 'D4342', frequency_limitation: 'Every 24 months', description: 'Periodontal scaling and root planing - one to three teeth per quadrant', category: 'Periodontics' },
      { procedure_code: 'D4910', frequency_limitation: 'Every 3 months', description: 'Periodontal maintenance', category: 'Periodontics' },
      
      // Prosthodontics - Removable
      { procedure_code: 'D5110', frequency_limitation: 'Every 5 years', description: 'Complete denture - maxillary', category: 'Prosthodontics' },
      { procedure_code: 'D5120', frequency_limitation: 'Every 5 years', description: 'Complete denture - mandibular', category: 'Prosthodontics' },
      { procedure_code: 'D5130', frequency_limitation: 'Every 5 years', description: 'Immediate denture - maxillary', category: 'Prosthodontics' },
      { procedure_code: 'D5140', frequency_limitation: 'Every 5 years', description: 'Immediate denture - mandibular', category: 'Prosthodontics' },
      { procedure_code: 'D5211', frequency_limitation: 'Every 5 years', description: 'Maxillary partial denture - resin base', category: 'Prosthodontics' },
      { procedure_code: 'D5212', frequency_limitation: 'Every 5 years', description: 'Mandibular partial denture - resin base', category: 'Prosthodontics' },
      { procedure_code: 'D5213', frequency_limitation: 'Every 5 years', description: 'Maxillary partial denture - cast metal framework', category: 'Prosthodontics' },
      { procedure_code: 'D5214', frequency_limitation: 'Every 5 years', description: 'Mandibular partial denture - cast metal framework', category: 'Prosthodontics' },
      
      // Prosthodontics - Fixed
      { procedure_code: 'D6240', frequency_limitation: 'Every 5 years', description: 'Pontic - porcelain fused to metal', category: 'Prosthodontics' },
      { procedure_code: 'D6241', frequency_limitation: 'Every 5 years', description: 'Pontic - porcelain fused to predominantly base metal', category: 'Prosthodontics' },
      { procedure_code: 'D6242', frequency_limitation: 'Every 5 years', description: 'Pontic - porcelain/ceramic', category: 'Prosthodontics' },
      { procedure_code: 'D6245', frequency_limitation: 'Every 5 years', description: 'Pontic - porcelain/ceramic substrate', category: 'Prosthodontics' },
      
      // Oral Surgery
      { procedure_code: 'D7140', frequency_limitation: 'Once per tooth', description: 'Extraction, erupted tooth or exposed root', category: 'Oral Surgery' },
      { procedure_code: 'D7210', frequency_limitation: 'Once per tooth', description: 'Extraction, erupted tooth requiring removal of bone and/or sectioning', category: 'Oral Surgery' },
      { procedure_code: 'D7220', frequency_limitation: 'Once per tooth', description: 'Removal of impacted tooth - soft tissue', category: 'Oral Surgery' },
      { procedure_code: 'D7230', frequency_limitation: 'Once per tooth', description: 'Removal of impacted tooth - partially bony', category: 'Oral Surgery' },
      { procedure_code: 'D7240', frequency_limitation: 'Once per tooth', description: 'Removal of impacted tooth - completely bony', category: 'Oral Surgery' },
      
      // Orthodontics
      { procedure_code: 'D8080', frequency_limitation: 'Once per lifetime', description: 'Comprehensive orthodontic treatment of the adolescent dentition', category: 'Orthodontics' },
      { procedure_code: 'D8090', frequency_limitation: 'Once per lifetime', description: 'Comprehensive orthodontic treatment of the adult dentition', category: 'Orthodontics' },
      
      // Space Maintainers
      { procedure_code: 'D1510', frequency_limitation: 'Once per space', description: 'Space maintainer - fixed - unilateral', category: 'Preventive' },
      { procedure_code: 'D1515', frequency_limitation: 'Once per space', description: 'Space maintainer - fixed - bilateral', category: 'Preventive' },
      { procedure_code: 'D1520', frequency_limitation: 'Once per space', description: 'Space maintainer - removable - unilateral', category: 'Preventive' },
      { procedure_code: 'D1525', frequency_limitation: 'Once per space', description: 'Space maintainer - removable - bilateral', category: 'Preventive' },
      
      // Sealants
      { procedure_code: 'D1351', frequency_limitation: 'Once per tooth', description: 'Sealant - per tooth', category: 'Preventive' },
      
      // Night Guards
      { procedure_code: 'D9940', frequency_limitation: 'Every 5 years', description: 'Occlusal guard, by report', category: 'Adjunctive General Services' },
      
      // Implants
      { procedure_code: 'D6010', frequency_limitation: 'Once per site', description: 'Surgical placement of implant body: endosteal implant', category: 'Implant Services' },
      { procedure_code: 'D6040', frequency_limitation: 'Once per implant', description: 'Surgical placement: eposteal implant', category: 'Implant Services' },
      { procedure_code: 'D6050', frequency_limitation: 'Once per implant', description: 'Surgical placement: transosteal implant', category: 'Implant Services' }
    ];
  }

  async run() {
    console.log('🔧 Implementing comprehensive frequency limitations...');
    
    const client = this.createClient();
    await client.connect();

    try {
      const frequencyLimitations = this.getStandardFrequencyLimitations();
      
      console.log(`📋 Processing ${frequencyLimitations.length} frequency limitations...`);
      
      let insertedCount = 0;
      let updatedCount = 0;
      let skippedCount = 0;

      for (const limitation of frequencyLimitations) {
        try {
          // Get procedure ID
          const procedureResult = await client.query(
            'SELECT id FROM procedures WHERE procedure_code = $1',
            [limitation.procedure_code]
          );

          if (procedureResult.rows.length === 0) {
            console.log(`  ⚠️  Procedure ${limitation.procedure_code} not found, skipping...`);
            skippedCount++;
            continue;
          }

          const procedureId = procedureResult.rows[0].id;

          // Apply to all carriers (or major carriers)
          const carriersResult = await client.query(
            'SELECT id FROM insurance_carriers ORDER BY id LIMIT 10'
          );

          for (const carrier of carriersResult.rows) {
            const carrierId = carrier.id;

            // Check if requirement already exists
            const existingResult = await client.query(
              'SELECT id, frequency_limitation FROM carrier_procedure_requirements WHERE carrier_id = $1 AND procedure_id = $2',
              [carrierId, procedureId]
            );

            if (existingResult.rows.length > 0) {
              // Update existing record if frequency_limitation is null or different
              const existing = existingResult.rows[0];
              if (!existing.frequency_limitation || existing.frequency_limitation !== limitation.frequency_limitation) {
                await client.query(`
                  UPDATE carrier_procedure_requirements 
                  SET frequency_limitation = $1, updated_at = NOW()
                  WHERE carrier_id = $2 AND procedure_id = $3
                `, [limitation.frequency_limitation, carrierId, procedureId]);
                updatedCount++;
              } else {
                skippedCount++;
              }
            } else {
              // Insert new record
              await client.query(`
                INSERT INTO carrier_procedure_requirements (
                  carrier_id, procedure_id, frequency_limitation, created_at, updated_at
                )
                VALUES ($1, $2, $3, NOW(), NOW())
              `, [carrierId, procedureId, limitation.frequency_limitation]);
              insertedCount++;
            }
          }

        } catch (error) {
          console.log(`  ❌ Error processing ${limitation.procedure_code}:`, error);
          skippedCount++;
        }
      }

      console.log('\n📊 Frequency Limitations Implementation Summary:');
      console.log(`  ✅ Inserted: ${insertedCount} new requirements`);
      console.log(`  🔄 Updated: ${updatedCount} existing requirements`);
      console.log(`  ⏭️  Skipped: ${skippedCount} requirements`);

      // Verify the results
      const totalResult = await client.query(`
        SELECT COUNT(*) as total_count 
        FROM carrier_procedure_requirements 
        WHERE frequency_limitation IS NOT NULL
      `);
      
      console.log(`\n🎯 Total frequency limitations in database: ${totalResult.rows[0].total_count}`);

    } finally {
      await client.end();
    }
  }
}

// Run the implementer
if (require.main === module) {
  const implementer = new FrequencyLimitationImplementer();
  implementer.run().catch(console.error);
}

#!/usr/bin/env ts-node

import { Client } from 'pg';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

class DocumentationRequirementsColumnMigration {
  private client: Client;

  constructor() {
    this.client = new Client({
      connectionString: process.env.POSTGRES_CONNECTION_STRING || process.env.DATABASE_URL
    });
  }

  async addMissingColumns(): Promise<void> {
    console.log('🔧 Adding missing columns to documentation_requirements table...');
    
    await this.client.connect();

    try {
      // Check current schema
      const currentColumns = await this.client.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'documentation_requirements'
        ORDER BY ordinal_position
      `);

      const existingColumns = currentColumns.rows.map(row => row.column_name);
      console.log('📋 Current columns:', existingColumns.join(', '));

      // Add category column if it doesn't exist
      if (!existingColumns.includes('category')) {
        console.log('➕ Adding category column...');
        await this.client.query(`
          ALTER TABLE documentation_requirements 
          ADD COLUMN category VARCHAR(100)
        `);
        console.log('✅ Category column added');
      } else {
        console.log('ℹ️  Category column already exists');
      }

      // Add priority column if it doesn't exist
      if (!existingColumns.includes('priority')) {
        console.log('➕ Adding priority column...');
        await this.client.query(`
          ALTER TABLE documentation_requirements 
          ADD COLUMN priority VARCHAR(20) DEFAULT 'medium'
        `);
        console.log('✅ Priority column added');
      } else {
        console.log('ℹ️  Priority column already exists');
      }

      // Update format_requirements column to be longer if needed
      console.log('🔧 Updating format_requirements column length...');
      await this.client.query(`
        ALTER TABLE documentation_requirements 
        ALTER COLUMN format_requirements TYPE TEXT
      `);
      console.log('✅ Format_requirements column updated to TEXT');

      // Verify the new schema
      const updatedColumns = await this.client.query(`
        SELECT 
          column_name,
          data_type,
          is_nullable,
          column_default
        FROM information_schema.columns 
        WHERE table_name = 'documentation_requirements'
        ORDER BY ordinal_position
      `);

      console.log('\n📊 Updated schema:');
      updatedColumns.rows.forEach((column, index) => {
        console.log(`  ${index + 1}. ${column.column_name} (${column.data_type})`);
        console.log(`     Nullable: ${column.is_nullable}`);
        if (column.column_default) {
          console.log(`     Default: ${column.column_default}`);
        }
      });

      // Update existing records with default categories
      console.log('\n🔄 Updating existing records with default categories...');
      
      const updateQueries = [
        {
          condition: "document_type ILIKE '%x-ray%' OR document_type ILIKE '%radiograph%'",
          category: 'radiographic_documentation',
          description: 'X-ray and radiographic requirements'
        },
        {
          condition: "document_type ILIKE '%chart%' OR document_type ILIKE '%periodontal%'",
          category: 'periodontal_documentation', 
          description: 'Periodontal charting requirements'
        },
        {
          condition: "document_type ILIKE '%medical%' OR document_type ILIKE '%history%'",
          category: 'medical_documentation',
          description: 'Medical history requirements'
        },
        {
          condition: "document_type ILIKE '%photo%' OR document_type ILIKE '%image%'",
          category: 'clinical_documentation',
          description: 'Clinical photography requirements'
        },
        {
          condition: "document_type ILIKE '%model%' OR document_type ILIKE '%impression%'",
          category: 'diagnostic_casts',
          description: 'Study models and impressions'
        }
      ];

      for (const update of updateQueries) {
        const result = await this.client.query(`
          UPDATE documentation_requirements 
          SET category = $1 
          WHERE category IS NULL AND (${update.condition})
        `, [update.category]);
        
        console.log(`  ✅ ${update.description}: ${result.rowCount} records updated`);
      }

      // Set default category for remaining records
      const remainingResult = await this.client.query(`
        UPDATE documentation_requirements 
        SET category = 'general_documentation' 
        WHERE category IS NULL
      `);
      
      console.log(`  ✅ General documentation: ${remainingResult.rowCount} records updated`);

      // Update priorities based on document types
      console.log('\n🎯 Setting priorities based on document importance...');
      
      const priorityUpdates = [
        {
          condition: "document_type ILIKE '%prior%' OR document_type ILIKE '%authorization%' OR document_type ILIKE '%preauth%'",
          priority: 'high',
          description: 'Prior authorization requirements'
        },
        {
          condition: "document_type ILIKE '%x-ray%' OR document_type ILIKE '%radiograph%'",
          priority: 'high',
          description: 'Radiographic requirements'
        },
        {
          condition: "document_type ILIKE '%medical%' OR document_type ILIKE '%necessity%'",
          priority: 'high',
          description: 'Medical necessity documentation'
        },
        {
          condition: "document_type ILIKE '%consent%' OR document_type ILIKE '%hipaa%'",
          priority: 'high',
          description: 'Compliance documentation'
        },
        {
          condition: "document_type ILIKE '%photo%' OR document_type ILIKE '%chart%'",
          priority: 'medium',
          description: 'Clinical documentation'
        }
      ];

      for (const update of priorityUpdates) {
        const result = await this.client.query(`
          UPDATE documentation_requirements 
          SET priority = $1 
          WHERE priority = 'medium' AND (${update.condition})
        `, [update.priority]);
        
        console.log(`  ✅ ${update.description}: ${result.rowCount} records updated to ${update.priority} priority`);
      }

      console.log('\n🎉 Documentation requirements table migration completed successfully!');

    } finally {
      await this.client.end();
    }
  }

  async run(): Promise<void> {
    console.log('🚀 Documentation Requirements Table Migration');
    console.log('=============================================\n');

    try {
      await this.addMissingColumns();
      
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }
}

// Main execution
if (require.main === module) {
  const migration = new DocumentationRequirementsColumnMigration();
  migration.run().catch(console.error);
}

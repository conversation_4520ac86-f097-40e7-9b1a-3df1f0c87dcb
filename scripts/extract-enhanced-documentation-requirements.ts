#!/usr/bin/env ts-node

import { Client } from 'pg';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

interface DocumentationRequirement {
  document_type: string;
  description: string;
  category: string;
  priority: 'high' | 'medium' | 'low';
  required_for?: string[];
  format_requirements?: string;
}

class EnhancedDocumentationExtractor {
  private client: Client;

  // Enhanced regex patterns from missing-clinical-documentation-types.md
  private enhancedPatterns = {
    // Bite Registration & Occlusal Analysis
    biteRegistration: /(?:bite.*?registration|centric.*?relation|occlusal.*?analysis).*?required/gi,
    jawRelation: /jaw.*?relation.*?record.*?required/gi,
    articulatorMounting: /articulator.*?mounting.*?required/gi,
    facebowTransfer: /facebow.*?transfer.*?required/gi,
    centricOcclusion: /centric.*?occlusion.*?record.*?required/gi,
    lateralExcursion: /lateral.*?excursion.*?record.*?required/gi,

    // Study Models & Impressions
    studyModels: /(?:study.*?models|diagnostic.*?casts|alginate.*?impressions).*?required/gi,
    preOpModels: /pre.*?operative.*?models.*?required/gi,
    postOpModels: /post.*?operative.*?models.*?required/gi,
    masterImpressions: /master.*?impressions.*?required/gi,
    opposingArchImpression: /opposing.*?arch.*?impression.*?required/gi,
    biteRegistrationWithModels: /bite.*?registration.*?with.*?models/gi,

    // Intraoral Photography
    intraoralPhotos: /(?:intraoral.*?photos|clinical.*?photographs|digital.*?images).*?required/gi,
    beforeAfterPhotos: /before.*?(?:and|&).*?after.*?photos.*?required/gi,
    retractionPhotos: /retraction.*?photos.*?required/gi,
    occlusalViewPhotos: /occlusal.*?view.*?photos.*?required/gi,
    facialProfilePhotos: /facial.*?profile.*?photos.*?required/gi,
    smileAnalysisPhotos: /smile.*?analysis.*?photos.*?required/gi,

    // Tissue Management Documentation
    tissueManagement: /tissue.*?management.*?protocol.*?required/gi,
    gingivalRetraction: /gingival.*?retraction.*?documented/gi,
    hemostasisAchieved: /hemostasis.*?achieved.*?documented/gi,
    tissueDisplacement: /tissue.*?displacement.*?technique.*?documented/gi,
    cordRetraction: /cord.*?retraction.*?method.*?documented/gi,

    // Coordination of Benefits (COB)
    coordinationOfBenefits: /coordination.*?of.*?benefits.*?(?:form|documentation)/gi,
    primarySecondaryInsurance: /primary.*?(?:and|&).*?secondary.*?insurance/gi,
    otherInsuranceCoverage: /other.*?insurance.*?coverage.*?declaration/gi,
    duplicateCoverageInquiry: /duplicate.*?coverage.*?inquiry/gi,
    benefitCoordination: /benefit.*?coordination.*?required/gi,
    nonDuplicationBenefits: /non.*?duplication.*?of.*?benefits/gi,

    // Prior Authorization Variations
    priorApproval: /prior.*?(?:approval|authorization|determination)/gi,
    treatmentPlanApproval: /treatment.*?plan.*?approval.*?required/gi,
    benefitsVerification: /benefits.*?verification.*?required/gi,
    coverageDetermination: /coverage.*?determination.*?request/gi,
    stepTherapy: /step.*?therapy.*?requirement/gi,
    utilizationReview: /utilization.*?review.*?required/gi,

    // Medical Necessity Documentation
    medicalNecessity: /medical.*?necessity.*?(?:statement|documentation)/gi,
    clinicalJustification: /clinical.*?justification.*?required/gi,
    treatmentRationale: /treatment.*?rationale.*?documented/gi,
    alternativeTreatment: /alternative.*?treatment.*?consideration/gi,
    conservativeTreatmentFailure: /conservative.*?treatment.*?failure.*?documented/gi,
    leastInvasiveApproach: /least.*?invasive.*?approach.*?attempted/gi,

    // Endodontic Documentation
    pulpVitality: /(?:pulp.*?vitality|percussion.*?test|palpation.*?test).*?documented/gi,
    workingLength: /working.*?length.*?determination.*?documented/gi,
    obturationTechnique: /obturation.*?technique.*?documented/gi,
    endodonticMicroscope: /endodontic.*?microscope.*?used/gi,
    apexLocator: /apex.*?locator.*?verification/gi,
    rubberDamIsolation: /rubber.*?dam.*?isolation.*?documented/gi,
    canalPreparation: /canal.*?preparation.*?technique.*?documented/gi,
    irrigationProtocol: /irrigation.*?protocol.*?documented/gi,

    // Periodontal Assessment
    bleedingOnProbing: /bleeding.*?on.*?probing.*?(?:scores|documented)/gi,
    gingivalIndex: /gingival.*?index.*?recorded/gi,
    plaqueIndex: /plaque.*?index.*?documented/gi,
    furcationInvolvement: /furcation.*?involvement.*?assessed/gi,
    clinicalAttachmentLevel: /clinical.*?attachment.*?level.*?measured/gi,
    probingDepthsSixSites: /probing.*?depths.*?six.*?sites.*?per.*?tooth/gi,
    gingivalRecession: /gingival.*?recession.*?measurements/gi,
    toothMobilityAssessment: /tooth.*?mobility.*?assessment.*?documented/gi,

    // Prosthodontic Requirements
    shadeSelection: /shade.*?selection.*?documented/gi,
    verticalDimensionAnalysis: /vertical.*?dimension.*?analysis/gi,
    occlusalScheme: /occlusal.*?scheme.*?documented/gi,
    provisionalRestoration: /provisional.*?restoration.*?evaluation/gi,
    marginPlacement: /margin.*?placement.*?documented/gi,
    tissueFinishLine: /tissue.*?finish.*?line.*?preparation/gi,
    functionalChewIn: /functional.*?chew.*?in.*?evaluation/gi,

    // Oral Surgery Documentation
    surgicalGuide: /surgical.*?guide.*?fabrication.*?required/gi,
    coneBeamCT: /cone.*?beam.*?ct.*?evaluation.*?required/gi,
    nerveProximity: /nerve.*?proximity.*?assessment.*?documented/gi,
    sinusEvaluation: /sinus.*?evaluation.*?completed/gi,
    boneGrafting: /bone.*?grafting.*?material.*?documented/gi,
    membranePlacement: /membrane.*?placement.*?documented/gi,
    suturingTechnique: /suturing.*?technique.*?documented/gi,

    // Pain Assessment
    painScale: /pain.*?scale.*?(?:documented|recorded|assessed)/gi,
    visualAnalogScale: /visual.*?analog.*?scale.*?used/gi,
    numericRatingScale: /numeric.*?rating.*?scale.*?documented/gi,
    painLevelBeforeAfter: /pain.*?level.*?before.*?(?:and|&).*?after/gi,
    analgesicEffectiveness: /analgesic.*?effectiveness.*?documented/gi,

    // Functional Assessment
    chewingFunction: /chewing.*?function.*?evaluated/gi,
    speechEvaluation: /speech.*?evaluation.*?documented/gi,
    aestheticEvaluation: /aesthetic.*?evaluation.*?completed/gi,
    patientSatisfaction: /patient.*?satisfaction.*?assessment/gi,
    qualityOfLife: /quality.*?of.*?life.*?evaluation/gi,
    masticatoryEfficiency: /masticatory.*?efficiency.*?assessed/gi,

    // Follow-up Requirements
    followUpVisit: /(?:follow.*?up|post.*?operative).*?(?:visit|appointment).*?scheduled/gi,
    healingAssessment: /healing.*?assessment.*?(?:required|documented)/gi,
    sutureRemoval: /suture.*?removal.*?scheduled/gi,
    recallAppointment: /recall.*?appointment.*?scheduled/gi,
    maintenanceProtocol: /maintenance.*?protocol.*?established/gi,
    complicationMonitoring: /complication.*?monitoring.*?required/gi,

    // Informed Consent Documentation
    informedConsent: /informed.*?consent.*?(?:obtained|documented|signed)/gi,
    treatmentAlternatives: /treatment.*?alternatives.*?discussed/gi,
    risksBenefits: /risks.*?(?:and|&).*?benefits.*?explained/gi,
    patientQuestions: /patient.*?questions.*?answered.*?documented/gi,
    refusalOfTreatment: /refusal.*?of.*?treatment.*?documented/gi,
    secondOpinion: /second.*?opinion.*?offered.*?documented/gi,

    // HIPAA & Privacy
    privacyNotice: /privacy.*?notice.*?acknowledged/gi,
    protectedHealthInfo: /protected.*?health.*?information.*?consent/gi,
    communicationPreferences: /communication.*?preferences.*?documented/gi,
    authorizationDisclosure: /authorization.*?for.*?disclosure/gi,
    minimumNecessary: /minimum.*?necessary.*?standard.*?applied/gi,

    // Emergency & Complications
    emergencyContact: /emergency.*?contact.*?information.*?updated/gi,
    allergiesReactions: /allergies.*?and.*?reactions.*?documented/gi,
    medicalAlertConditions: /medical.*?alert.*?conditions.*?noted/gi,
    complicationManagement: /complication.*?management.*?protocol/gi,
    adverseEventReporting: /adverse.*?event.*?reporting.*?completed/gi,

    // Laser Therapy Documentation
    laserTherapyParameters: /laser.*?therapy.*?(?:parameters|settings).*?documented/gi,
    wavelengthPowerSettings: /wavelength.*?and.*?power.*?settings.*?recorded/gi,
    laserSafetyProtocol: /laser.*?safety.*?protocol.*?followed/gi,
    tissueResponseLaser: /tissue.*?response.*?to.*?laser.*?documented/gi,

    // Sedation & Anesthesia
    sedationMonitoring: /(?:sedation|anesthesia).*?monitoring.*?documented/gi,
    vitalSignsRecorded: /vital.*?signs.*?recorded.*?during.*?procedure/gi,
    recoveryMonitoring: /recovery.*?monitoring.*?documented/gi,
    preOperativeFasting: /pre.*?operative.*?fasting.*?confirmed/gi,
    dischargeCriteria: /discharge.*?criteria.*?met.*?documented/gi,
    escortRequirement: /escort.*?requirement.*?confirmed/gi,

    // Implant-Specific Documentation
    boneDensityAssessment: /bone.*?density.*?assessment.*?completed/gi,
    implantPlacementTorque: /implant.*?placement.*?torque.*?documented/gi,
    osseointegrationAssessment: /osseointegration.*?assessment.*?documented/gi,
    implantStabilityQuotient: /implant.*?stability.*?quotient.*?measured/gi,
    tissueThicknessMeasurement: /tissue.*?thickness.*?measurement.*?documented/gi,
    adjacentToothVitality: /adjacent.*?tooth.*?vitality.*?confirmed/gi,
    implantPlatformSize: /implant.*?platform.*?size.*?documented/gi,
    surgicalTemplateUsed: /surgical.*?template.*?used.*?documented/gi,

    // Healing Time Requirements
    healingTimeMinimum: /healing.*?time.*?(?:minimum|required).*?(\d+).*?(?:weeks|months)/gi,
    osseointegrationPeriod: /osseointegration.*?period.*?(\d+).*?months/gi,
    tissueMaturationPeriod: /tissue.*?maturation.*?period.*?(\d+).*?weeks/gi,
    boneRemodelingTime: /bone.*?remodeling.*?time.*?(\d+).*?months/gi,

    // Provider Qualification Requirements
    boardCertifiedSpecialist: /board.*?certified.*?(?:specialist|periodontist|endodontist)/gi,
    residencyTrainedProvider: /residency.*?trained.*?provider.*?required/gi,
    continuingEducation: /continuing.*?education.*?certification.*?required/gi,
    specialtyTraining: /specialty.*?training.*?documentation.*?required/gi,
    hospitalPrivileges: /hospital.*?privileges.*?verification.*?required/gi,

    // Treatment Sequencing
    phasedTreatment: /phased.*?treatment.*?approach.*?documented/gi,
    treatmentSequence: /treatment.*?sequence.*?optimization/gi,
    healingBetweenPhases: /healing.*?between.*?phases.*?required/gi,
    interimRestoration: /interim.*?restoration.*?placement/gi,
    provisionalPeriod: /provisional.*?period.*?duration.*?specified/gi,

    // Digital Workflow Documentation
    digitalImpression: /digital.*?impression.*?technique.*?used/gi,
    cadCamRestoration: /cad.*?cam.*?restoration.*?fabricated/gi,
    digitalSmileDesign: /digital.*?smile.*?design.*?completed/gi,
    virtualTreatmentPlanning: /virtual.*?treatment.*?planning.*?utilized/gi,
    opticalImpressionAccuracy: /optical.*?impression.*?accuracy.*?verified/gi,

    // AI & Technology-Assisted Procedures
    computerGuidedSurgery: /computer.*?guided.*?surgery.*?utilized/gi,
    roboticAssistance: /robotic.*?assistance.*?employed/gi,
    aiAidedDiagnosis: /artificial.*?intelligence.*?aided.*?diagnosis/gi,
    machineLearningRisk: /machine.*?learning.*?risk.*?assessment/gi,
    digitalWorkflowValidation: /digital.*?workflow.*?validation.*?completed/gi,

    // Telemedicine & Remote Monitoring
    telemedicineConsultation: /telemedicine.*?consultation.*?completed/gi,
    remoteMonitoring: /remote.*?monitoring.*?protocol.*?established/gi,
    virtualFollowUp: /virtual.*?follow.*?up.*?scheduled/gi,
    digitalPatientCommunication: /digital.*?patient.*?communication.*?documented/gi,

    // Laboratory Work Authorization
    laboratoryPrescription: /laboratory.*?prescription.*?completed/gi,
    shadeCommunicationLab: /shade.*?communication.*?to.*?lab/gi,
    laboratoryWorkAuthorization: /laboratory.*?work.*?authorization.*?signed/gi,
    qualityControlStandards: /quality.*?control.*?standards.*?specified/gi,
    deliveryTimelineConfirmed: /delivery.*?timeline.*?confirmed.*?with.*?lab/gi,

    // Specialist Referral Documentation
    specialistReferral: /specialist.*?referral.*?(?:sent|completed)/gi,
    referralLetterComplete: /referral.*?letter.*?includes.*?complete.*?information/gi,
    specialistReportReceived: /specialist.*?report.*?received.*?and.*?reviewed/gi,
    collaborativeTreatmentPlan: /collaborative.*?treatment.*?plan.*?established/gi,
    returnToReferringDoctor: /return.*?to.*?referring.*?doctor.*?protocol/gi,

    // Payment Plan Documentation
    paymentPlanAgreement: /payment.*?plan.*?agreement.*?signed/gi,
    financialArrangements: /financial.*?arrangements.*?documented/gi,
    insuranceBenefitsExplanation: /insurance.*?benefits.*?explanation.*?provided/gi,
    treatmentCostEstimate: /treatment.*?cost.*?estimate.*?provided/gi,
    alternativeFinancing: /alternative.*?financing.*?options.*?discussed/gi,

    // Quality Assurance Patterns
    qualityAssuranceProtocol: /quality.*?assurance.*?protocol.*?followed/gi,
    clinicalOutcomeMeasurement: /clinical.*?outcome.*?measurement.*?documented/gi,
    patientSafetyChecklist: /patient.*?safety.*?checklist.*?completed/gi,
    infectionControlProtocol: /infection.*?control.*?protocol.*?documented/gi,
    sterilizationVerification: /sterilization.*?verification.*?documented/gi
  };

  constructor() {
    this.client = new Client({
      connectionString: process.env.POSTGRES_CONNECTION_STRING || process.env.DATABASE_URL
    });
  }

  private createDocumentationType(patternName: string, match: string): DocumentationRequirement {
    // Map pattern names to human-readable document types and categories
    const typeMapping: Record<string, { type: string; category: string; priority: 'high' | 'medium' | 'low' }> = {
      biteRegistration: { type: 'Bite Registration', category: 'occlusal_analysis', priority: 'high' },
      studyModels: { type: 'Study Models', category: 'diagnostic_casts', priority: 'medium' },
      intraoralPhotos: { type: 'Intraoral Photography', category: 'clinical_documentation', priority: 'high' },
      tissueManagement: { type: 'Tissue Management Protocol', category: 'surgical_documentation', priority: 'medium' },
      coordinationOfBenefits: { type: 'Coordination of Benefits', category: 'insurance_administration', priority: 'high' },
      priorApproval: { type: 'Prior Authorization', category: 'insurance_administration', priority: 'high' },
      medicalNecessity: { type: 'Medical Necessity Documentation', category: 'clinical_justification', priority: 'high' },
      pulpVitality: { type: 'Endodontic Assessment', category: 'endodontic_documentation', priority: 'high' },
      bleedingOnProbing: { type: 'Periodontal Assessment', category: 'periodontal_documentation', priority: 'high' },
      shadeSelection: { type: 'Prosthodontic Documentation', category: 'prosthodontic_requirements', priority: 'medium' },
      surgicalGuide: { type: 'Oral Surgery Documentation', category: 'surgical_documentation', priority: 'high' },
      painScale: { type: 'Pain Assessment', category: 'quality_measures', priority: 'medium' },
      chewingFunction: { type: 'Functional Assessment', category: 'outcome_tracking', priority: 'medium' },
      followUpVisit: { type: 'Follow-up Requirements', category: 'post_treatment_care', priority: 'medium' },
      informedConsent: { type: 'Informed Consent', category: 'compliance_documentation', priority: 'high' },
      privacyNotice: { type: 'HIPAA Documentation', category: 'privacy_compliance', priority: 'high' },
      emergencyContact: { type: 'Emergency Protocols', category: 'safety_documentation', priority: 'medium' },
      laserTherapyParameters: { type: 'Laser Therapy Documentation', category: 'advanced_procedures', priority: 'medium' },
      sedationMonitoring: { type: 'Sedation Documentation', category: 'anesthesia_monitoring', priority: 'high' },
      boneDensityAssessment: { type: 'Implant Documentation', category: 'implant_procedures', priority: 'high' },
      healingTimeMinimum: { type: 'Healing Time Requirements', category: 'temporal_requirements', priority: 'medium' },
      boardCertifiedSpecialist: { type: 'Provider Qualifications', category: 'provider_requirements', priority: 'medium' },
      phasedTreatment: { type: 'Treatment Sequencing', category: 'treatment_planning', priority: 'medium' },
      digitalImpression: { type: 'Digital Workflow', category: 'modern_technology', priority: 'low' },
      computerGuidedSurgery: { type: 'AI-Assisted Procedures', category: 'technology_integration', priority: 'low' },
      telemedicineConsultation: { type: 'Telemedicine Documentation', category: 'remote_care', priority: 'low' },
      laboratoryPrescription: { type: 'Laboratory Work Authorization', category: 'external_services', priority: 'medium' },
      specialistReferral: { type: 'Specialist Referral', category: 'collaborative_care', priority: 'medium' },
      paymentPlanAgreement: { type: 'Financial Documentation', category: 'administrative', priority: 'low' },
      qualityAssuranceProtocol: { type: 'Quality Assurance', category: 'quality_control', priority: 'medium' }
    };

    const mapping = typeMapping[patternName] || { 
      type: patternName.replace(/([A-Z])/g, ' $1').trim(), 
      category: 'general_documentation', 
      priority: 'medium' as const 
    };

    return {
      document_type: mapping.type,
      description: match.trim(),
      category: mapping.category,
      priority: mapping.priority,
      format_requirements: this.getFormatRequirements(mapping.category)
    };
  }

  private getFormatRequirements(category: string): string {
    const formatMap: Record<string, string> = {
      'clinical_documentation': 'Digital images in JPEG format, minimum 1024x768 resolution',
      'diagnostic_casts': 'Physical models or digital scans with occlusal registration',
      'insurance_administration': 'Completed forms with all required fields and signatures',
      'endodontic_documentation': 'Pre and post-operative radiographs with working length confirmation',
      'periodontal_documentation': 'Six-point probing depths per tooth with bleeding indices',
      'surgical_documentation': 'Pre-operative planning documents and post-operative reports',
      'implant_procedures': 'CBCT scans, surgical guides, and torque measurements',
      'compliance_documentation': 'Signed forms with witness signatures where required',
      'quality_measures': 'Standardized assessment scales with numerical ratings'
    };

    return formatMap[category] || 'Standard documentation format as per practice protocols';
  }

  async extractFromGuidelines(): Promise<void> {
    console.log('🔍 Extracting enhanced documentation requirements from guidelines...');
    
    await this.client.connect();

    try {
      // Get all guidelines content
      const guidelinesResult = await this.client.query(`
        SELECT id, carrier_id, title, content 
        FROM guidelines 
        ORDER BY id
      `);

      console.log(`📋 Processing ${guidelinesResult.rows.length} guidelines...`);

      let totalExtracted = 0;
      let newRequirements = 0;

      for (const guideline of guidelinesResult.rows) {
        const content = typeof guideline.content === 'string' 
          ? guideline.content 
          : JSON.stringify(guideline.content);

        const extractedRequirements = this.extractRequirementsFromContent(content);
        
        if (extractedRequirements.length > 0) {
          console.log(`  📄 ${guideline.title}: Found ${extractedRequirements.length} requirements`);
          
          for (const requirement of extractedRequirements) {
            try {
              // Check if this requirement already exists
              const existingCheck = await this.client.query(`
                SELECT id FROM documentation_requirements
                WHERE document_type = $1 AND description = $2
                LIMIT 1
              `, [requirement.document_type, requirement.description]);

              if (existingCheck.rows.length === 0) {
                await this.client.query(`
                  INSERT INTO documentation_requirements
                  (carrier_id, document_type, description, category, priority, format_requirements)
                  VALUES ($1, $2, $3, $4, $5, $6)
                `, [
                guideline.carrier_id,
                requirement.document_type,
                requirement.description,
                requirement.category,
                requirement.priority,
                requirement.format_requirements
              ]);
              
                newRequirements++;
              } else {
                // Skip duplicate
              }
            } catch (error) {
              // Skip duplicates silently
              const errorMessage = error instanceof Error ? error.message : String(error);
              console.error(`    ❌ Error inserting requirement: ${errorMessage}`);
            }
          }
          
          totalExtracted += extractedRequirements.length;
        }
      }

      console.log(`\n✅ Enhanced extraction completed:`);
      console.log(`   📊 Total requirements found: ${totalExtracted}`);
      console.log(`   ➕ New requirements added: ${newRequirements}`);
      console.log(`   🔄 Duplicates skipped: ${totalExtracted - newRequirements}`);

    } finally {
      await this.client.end();
    }
  }

  private extractRequirementsFromContent(content: string): DocumentationRequirement[] {
    const requirements: DocumentationRequirement[] = [];

    Object.entries(this.enhancedPatterns).forEach(([patternName, pattern]) => {
      const matches = content.match(pattern);
      
      if (matches) {
        matches.forEach(match => {
          const requirement = this.createDocumentationType(patternName, match);
          requirements.push(requirement);
        });
      }
    });

    return requirements;
  }

  async run(): Promise<void> {
    console.log('🚀 Enhanced Documentation Requirements Extraction');
    console.log('================================================\n');

    try {
      await this.extractFromGuidelines();
      console.log('\n🎉 Enhanced documentation requirements extraction completed successfully!');
      
    } catch (error) {
      console.error('❌ Enhanced extraction failed:', error);
      throw error;
    }
  }
}

// Main execution
if (require.main === module) {
  const extractor = new EnhancedDocumentationExtractor();
  extractor.run().catch(console.error);
}
